import Fastify from 'fastify';
import WebSocket from 'ws';
import dotenv from 'dotenv';
import fastifyFormBody from '@fastify/formbody';
import fastifyWs from '@fastify/websocket';
import fastifyCors from '@fastify/cors';
import fastifyStatic from '@fastify/static';
import twilio from 'twilio';
import fs from 'fs';
import { readFile, writeFile, access } from 'fs/promises';
import path from 'path';
import { mkdir } from 'fs/promises';
import { dirname } from 'path';
import { fileURLToPath } from 'url';
import fetch from 'node-fetch';
import pkg from '@google/genai';
const { GoogleGenAI, Modality } = pkg;
import { parsePhoneNumber } from 'libphonenumber-js';
import { getTimezone } from 'countries-and-timezones';
// Deepgram for speech-to-text transcription
import { createClient } from '@deepgram/sdk';
// Enhanced audio processing imports
import { validateSupabaseAuth } from './middleware/auth-simple.js';
import pcmConvert from 'pcm-convert';
import audioLena from 'audio-lena';
import audioDecode from 'audio-decode';
import speexResampler from 'speex-resampler';
import AudioEnhancer from './audio-enhancer.js';
// Import NEW separate incoming system
import {
    getIncomingScenario,
    listIncomingScenarios,
    setActiveIncomingScenario,
    getCurrentIncomingScenario,
    createCustomIncomingScenario,
    recordIncomingCallStart,
    recordIncomingCallEnd,
    getIncomingAnalytics,
    getIncomingCallHistory
} from './incoming-system.js';

// Outbound call script system imports (when app user calls someone else)
import {
    getIncomingCallScript as getOutboundCallScript,
    listIncomingCallScripts as listOutboundCallScripts,
    setIncomingCallScript as setOutboundCallScript,
    getCurrentIncomingScript as getCurrentOutboundScript,
    createCustomIncomingScript as createCustomOutboundScript,
    getScriptMetrics,
    recordCallStart,
    recordCallEnd,
    getCallHistory
} from './incoming-call-scripts.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

// Configuration
const {
    GEMINI_API_KEY,
    PORT,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    PUBLIC_URL,
    // DEFAULT_SYSTEM_PROMPT removed - using campaign scripts only
    SUMMARY_GENERATION_PROMPT,
    AI_PREPARE_MESSAGE,
    AI_PREPARE_MESSAGE_OUTBOUND,
    AI_PREPARE_MESSAGE_INCOMING,
    GREETING_AUDIO_URL
} = process.env;

console.log('🚀 Twilio Gemini Live API Server starting...');
console.log('📝 Using Gemini API Key:', GEMINI_API_KEY ? 'SET ✅' : 'NOT SET ❌');
console.log('📞 Twilio Config:', TWILIO_ACCOUNT_SID ? 'SET ✅' : 'NOT SET ❌');
console.log('🎤 Deepgram API Key:', process.env.DEEPGRAM_API_KEY ? 'SET ✅' : 'NOT SET ❌');

// Initialize Deepgram client for transcription
const deepgramClient = process.env.DEEPGRAM_API_KEY ? createClient(process.env.DEEPGRAM_API_KEY) : null;

// Deepgram transcription helper
class TranscriptionManager {
    static async initializeTranscription(callSid, connectionData) {
        if (!deepgramClient) {
            console.warn(`⚠️ [${callSid}] Deepgram not available - transcription disabled`);
            return null;
        }

        if (!connectionData) {
            console.warn(`⚠️ [${callSid}] No connection data - transcription disabled`);
            return null;
        }

        try {
            const dgConnection = deepgramClient.listen.live({
                model: 'nova-2',
                language: 'en',
                smart_format: true,
                interim_results: true,
                utterance_end_ms: 1000,
                vad_events: true,
                encoding: 'mulaw',
                sample_rate: 8000,
                channels: 1
            });

            dgConnection.on('open', () => {
                console.log(`🎤 [${callSid}] Deepgram transcription connection opened`);
            });

            dgConnection.on('transcript', (data) => {
                try {
                    const transcript = data.channel?.alternatives?.[0]?.transcript;
                    if (transcript && data.is_final) {
                        console.log(`📝 [${callSid}] User said: "${transcript}"`);

                        // Add to full transcript log (safe check)
                        if (connectionData && connectionData.fullTranscript) {
                            connectionData.fullTranscript.push({
                                role: 'user',
                                content: transcript,
                                timestamp: Date.now(),
                                confidence: data.channel.alternatives[0].confidence || 0.9
                            });
                        }
                    }
                } catch (error) {
                    console.warn(`⚠️ [${callSid}] Deepgram transcript processing error:`, error);
                }
            });

            dgConnection.on('error', (error) => {
                console.error(`❌ [${callSid}] Deepgram transcription error:`, error);
            });

            dgConnection.on('close', () => {
                console.log(`🔌 [${callSid}] Deepgram transcription connection closed`);
            });

            return dgConnection;
        } catch (error) {
            console.error(`❌ [${callSid}] Failed to initialize Deepgram transcription:`, error);
            return null;
        }
    }

    static closeTranscription(callSid, dgConnection) {
        if (dgConnection && typeof dgConnection.finish === 'function') {
            try {
                dgConnection.finish();
                console.log(`🔌 [${callSid}] Deepgram transcription closed`);
            } catch (error) {
                console.warn(`⚠️ [${callSid}] Error closing Deepgram transcription:`, error);
            }
        }
    }
}

// Campaign Script Policy Implementation
// According to CAMPAIGN_SCRIPT_POLICY.md, we send the full campaign script directly to Gemini
// No extraction or parsing needed - just send the complete script as-is

/**
 * Convert legacy incoming scenario to campaign script format
 * @param {Object} incomingScenario - Legacy incoming scenario object
 * @returns {Object} - Campaign script format
 */
function convertIncomingScenarioToCampaignScript(incomingScenario) {
    // Create a campaign script structure from the legacy incoming scenario
    const campaignScript = {
        id: incomingScenario.id,
        type: 'incoming',
        language: 'en', // Default, could be enhanced
        category: incomingScenario.category || 'support',
        title: incomingScenario.name,
        campaign: incomingScenario.description,

        agentPersona: {
            name: incomingScenario.agent?.name || 'AI Assistant',
            role: 'Customer Service Representative',
            company: 'Verduona',
            tone: incomingScenario.agent?.tone || 'Professional, helpful',
            behavioralGuidelines: {
                speakingStyle: 'Natural, conversational, no robotic speech',
                interruptionHandling: 'Stop immediately, acknowledge, continue',
                pauseHandling: 'Allow natural pauses, don\'t rush',
                handleInterjections: 'If the caller speaks while you are talking, stop immediately. Acknowledge their point briefly and let them finish.',
                empathyFirst: incomingScenario.agent?.behavior?.empathyLevel === 'high'
            }
        },

        callContext: {
            purpose: 'Handle incoming customer service call',
            callDirection: 'customer_calls_agent',
            expectedDuration: '3-10 minutes',
            successCriteria: 'Resolve customer inquiry or route to appropriate specialist'
        },

        script: {
            start: [
                { type: 'statement', content: `Hello, this is ${incomingScenario.agent?.name || 'AI Assistant'}. How can I help you today?` },
                { type: 'listen', responseVariable: 'customerNeed', timeout: 15 },
                { type: 'goTo', target: 'assessSituation' }
            ],
            assessSituation: [
                { type: 'statement', content: 'I understand. Let me help you with that.' },
                { type: 'goTo', target: 'handleRequest' }
            ],
            handleRequest: [
                { type: 'statement', content: 'I\'ll do my best to assist you with your request.' }
            ]
        }
    };

    return campaignScript;
}

// Use the summary prompt from .env, with a basic fallback
const ACTUAL_SUMMARY_PROMPT = SUMMARY_GENERATION_PROMPT || "Report campaign related result and important context or follow up.";
const SUMMARY_TIMEOUT_MS = 60000;

const DEFAULT_VOICE = process.env.GEMINI_DEFAULT_VOICE || 'Orus';
const VOICE_SELECTION_ENABLED = process.env.GEMINI_VOICE_SELECTION_ENABLED === 'true';

// Available Gemini voices with characteristics (Updated June 11, 2025)
const AVAILABLE_GEMINI_VOICES = {
    'Aoede': {
        name: 'Aoede',
        gender: 'Female',
        characteristics: 'bright, neutral narrator',
        pitch: 'neutral',
        timbre: 'bright',
        persona: 'narrator'
    },
    'Puck': {
        name: 'Puck',
        gender: 'Male',
        characteristics: 'lively, higher tenor',
        pitch: 'higher',
        timbre: 'lively',
        persona: 'energetic'
    },
    'Charon': {
        name: 'Charon',
        gender: 'Male',
        characteristics: 'deep, warm baritone',
        pitch: 'deep',
        timbre: 'warm',
        persona: 'authoritative'
    },
    'Kore': {
        name: 'Kore',
        gender: 'Female',
        characteristics: 'soft alto, empathetic',
        pitch: 'alto',
        timbre: 'soft',
        persona: 'empathetic'
    },
    'Fenrir': {
        name: 'Fenrir',
        gender: 'Male',
        characteristics: 'assertive mid-range',
        pitch: 'mid-range',
        timbre: 'assertive',
        persona: 'confident'
    },
    'Leda': {
        name: 'Leda',
        gender: 'Female',
        characteristics: 'clear RP-style announcer',
        pitch: 'clear',
        timbre: 'professional',
        persona: 'announcer'
    },
    'Orus': {
        name: 'Orus',
        gender: 'Male',
        characteristics: 'relaxed, breathy tenor',
        pitch: 'tenor',
        timbre: 'breathy',
        persona: 'relaxed'
    },
    'Zephyr': {
        name: 'Zephyr',
        gender: 'Female',
        characteristics: 'airy, youthful soprano',
        pitch: 'soprano',
        timbre: 'airy',
        persona: 'youthful'
    }
};

// Voice mapping for different languages/accents and OpenAI compatibility
const VOICE_MAPPING = {
    // OpenAI voice mappings
    'shimmer': 'Orus',      // OpenAI shimmer → Gemini Orus (relaxed, breathy tenor)
    'alloy': 'Puck',        // OpenAI alloy → Gemini Puck (lively, higher tenor)
    'echo': 'Charon',       // OpenAI echo → Gemini Charon (deep, warm baritone)
    'fable': 'Kore',        // OpenAI fable → Gemini Kore (soft alto, empathetic)
    'onyx': 'Fenrir',       // OpenAI onyx → Gemini Fenrir (assertive mid-range)
    'nova': 'Aoede',        // OpenAI nova → Gemini Aoede (bright, neutral narrator)

    // Gender-based mappings
    'female': 'Kore',       // Default female → empathetic
    'male': 'Orus',         // Default male → relaxed
    'professional': 'Leda', // Professional → RP-style announcer
    'youthful': 'Zephyr',   // Youthful → airy, youthful soprano
    'authoritative': 'Charon', // Authoritative → deep, warm baritone
    'energetic': 'Puck'     // Energetic → lively, higher tenor
};

// Function to map voice names and validate
function getValidGeminiVoice(requestedVoice) {
    if (!requestedVoice) {
        console.log(`🎤 No voice specified, using default: ${DEFAULT_VOICE} (${AVAILABLE_GEMINI_VOICES[DEFAULT_VOICE]?.characteristics || 'unknown'})`);
        return DEFAULT_VOICE;
    }

    // Check if it's already a valid Gemini voice
    if (AVAILABLE_GEMINI_VOICES[requestedVoice]) {
        const voiceInfo = AVAILABLE_GEMINI_VOICES[requestedVoice];
        console.log(`🎤 Using requested voice: ${requestedVoice} (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
        return requestedVoice;
    }

    // Check if it's an OpenAI voice or other mapping that needs conversion
    if (VOICE_MAPPING[requestedVoice]) {
        const mappedVoice = VOICE_MAPPING[requestedVoice];
        const voiceInfo = AVAILABLE_GEMINI_VOICES[mappedVoice];
        console.log(`🎤 Mapped voice '${requestedVoice}' → '${mappedVoice}' (${voiceInfo.gender}, ${voiceInfo.characteristics})`);
        return mappedVoice;
    }

    // Default fallback
    const defaultInfo = AVAILABLE_GEMINI_VOICES[DEFAULT_VOICE];
    console.log(`⚠️ Unknown voice '${requestedVoice}', using default: ${DEFAULT_VOICE} (${defaultInfo?.characteristics || 'unknown'})`);
    return DEFAULT_VOICE;
}

// Function to validate and get Gemini model
function getValidGeminiModel(requestedModel) {
    if (!requestedModel) {
        console.log(`🤖 No model specified, using default: ${DEFAULT_GEMINI_MODEL}`);
        return DEFAULT_GEMINI_MODEL;
    }

    // Check if it's a valid Gemini model
    if (AVAILABLE_GEMINI_MODELS[requestedModel]) {
        console.log(`🤖 Using requested model: ${requestedModel}`);
        return requestedModel;
    }

    // Default fallback
    console.log(`⚠️ Unknown model '${requestedModel}', using default: ${DEFAULT_GEMINI_MODEL}`);
    return DEFAULT_GEMINI_MODEL;
}
// Model definitions with metadata (Updated June 11, 2025)
const MODEL_DEFINITIONS = {
    'gemini-2.5-flash-preview-native-audio-dialog': {
        name: 'Gemini 2.5 Flash - Native Audio Dialog',
        description: 'Recommended default for voice apps. Outputs text and 24 kHz speech in 30 HD voices across 24 languages',
        status: 'Private preview',
        supportsAudio: true,
        quality: '★★★',
        region: 'global'
    },
    'gemini-2.0-flash-live-001': {
        name: 'Gemini 2.0 Flash Live (001)',
        description: 'GA/billing-enabled half-cascade audio. Native audio in, server-side TTS out',
        status: 'Public preview (billing on)',
        supportsAudio: true,
        quality: '★★',
        region: 'global, us-central1, EU4'
    }
};

// Load configuration from environment variables
const DEFAULT_GEMINI_MODEL = process.env.GEMINI_DEFAULT_MODEL || 'gemini-2.5-flash-preview-native-audio-dialog';
const AVAILABLE_MODEL_IDS = process.env.GEMINI_AVAILABLE_MODELS ?
    process.env.GEMINI_AVAILABLE_MODELS.split(',').map(m => m.trim()) :
    ['gemini-2.5-flash-preview-native-audio-dialog', 'gemini-2.0-flash-live-001'];
const MODEL_SELECTION_ENABLED = process.env.GEMINI_MODEL_SELECTION_ENABLED === 'true';

// Build available models object from environment configuration
const AVAILABLE_GEMINI_MODELS = {};
AVAILABLE_MODEL_IDS.forEach(modelId => {
    if (MODEL_DEFINITIONS[modelId]) {
        AVAILABLE_GEMINI_MODELS[modelId] = MODEL_DEFINITIONS[modelId];
    } else {
        console.warn(`⚠️ Unknown model ID in GEMINI_AVAILABLE_MODELS: ${modelId}`);
    }
});

// Validate that default model is in available models
if (!AVAILABLE_GEMINI_MODELS[DEFAULT_GEMINI_MODEL]) {
    console.error(`❌ Default model '${DEFAULT_GEMINI_MODEL}' not found in available models. Using fallback.`);
    const fallbackModel = Object.keys(AVAILABLE_GEMINI_MODELS)[0] || 'gemini-2.5-flash-preview-native-audio-dialog';
    console.log(`🔄 Using fallback model: ${fallbackModel}`);
}
let CURRENT_GEMINI_MODEL = DEFAULT_GEMINI_MODEL;

// Enhanced Audio conversion utilities for Twilio μ-law ↔ Gemini PCM with quality improvements
class AudioProcessor {
    // Static audio enhancer instance
    static audioEnhancer = new AudioEnhancer();
    /**
     * Converts Twilio's G.711 μ-law audio to Int16 PCM format for Gemini compatibility.
     * Enhanced with noise reduction and quality improvements.
     * @param audioBuffer - Input audio data in μ-law format from Twilio
     * @returns Converted audio data in Int16 PCM format
     */
    static convertUlawToPCM(audioBuffer) {
        try {
            // G.711 μ-law to linear PCM conversion table
            const ULAW_TO_LINEAR = new Int16Array(256);
            for (let i = 0; i < 256; i++) {
                const ulaw = ~i;
                let t = ((ulaw & 0x0F) << 3) + 0x84;
                t <<= ((ulaw & 0x70) >> 4);
                ULAW_TO_LINEAR[i] = (ulaw & 0x80) ? (0x84 - t) : (t - 0x84);
            }

            // Convert G.711 μ-law to 16-bit PCM
            const int16Buffer = Buffer.alloc(audioBuffer.length * 2);
            for (let i = 0; i < audioBuffer.length; i++) {
                const ulawSample = audioBuffer[i];
                const pcmSample = ULAW_TO_LINEAR[ulawSample];
                int16Buffer.writeInt16LE(pcmSample, i * 2);
            }

            // Apply advanced audio enhancement
            return AudioProcessor.enhanceAudioQualityAdvanced(int16Buffer);
        } catch (error) {
            console.error('❌ Error in μ-law to PCM conversion:', error);
            // Fallback to basic conversion without enhancement
            return AudioProcessor.basicUlawToPCM(audioBuffer);
        }
    }

    /**
     * Basic μ-law to PCM conversion without enhancements (fallback)
     */
    static basicUlawToPCM(audioBuffer) {
        const ULAW_TO_LINEAR = new Int16Array(256);
        for (let i = 0; i < 256; i++) {
            const ulaw = ~i;
            let t = ((ulaw & 0x0F) << 3) + 0x84;
            t <<= ((ulaw & 0x70) >> 4);
            ULAW_TO_LINEAR[i] = (ulaw & 0x80) ? (0x84 - t) : (t - 0x84);
        }

        const int16Buffer = Buffer.alloc(audioBuffer.length * 2);
        for (let i = 0; i < audioBuffer.length; i++) {
            const ulawSample = audioBuffer[i];
            const pcmSample = ULAW_TO_LINEAR[ulawSample];
            int16Buffer.writeInt16LE(pcmSample, i * 2);
        }
        return int16Buffer;
    }

    /**
     * Advanced audio quality improvement using sophisticated algorithms
     * @param pcmBuffer - Raw PCM audio buffer
     * @returns Enhanced PCM audio buffer
     */
    static enhanceAudioQualityAdvanced(pcmBuffer) {
        try {
            // Convert to float32 for processing
            const samples = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < samples.length; i++) {
                samples[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            // Apply advanced audio enhancement
            const enhancementOptions = {
                noiseReduction: process.env.AUDIO_NOISE_REDUCTION !== 'false',
                compression: process.env.AUDIO_COMPRESSION !== 'false',
                agc: process.env.AUDIO_AGC !== 'false',
                voiceEnhancement: process.env.AUDIO_VOICE_ENHANCEMENT !== 'false'
            };

            const enhancedSamples = AudioProcessor.audioEnhancer.enhance(samples, enhancementOptions);

            // Convert back to int16 PCM
            const enhancedBuffer = Buffer.alloc(pcmBuffer.length);
            for (let i = 0; i < enhancedSamples.length; i++) {
                const sample = Math.max(-1, Math.min(1, enhancedSamples[i]));
                enhancedBuffer.writeInt16LE(Math.round(sample * 32767), i * 2);
            }

            return enhancedBuffer;
        } catch (error) {
            console.error('❌ Error in advanced audio enhancement:', error);
            return AudioProcessor.enhanceAudioQuality(pcmBuffer); // Fallback to basic enhancement
        }
    }

    /**
     * Basic audio quality improvement with noise reduction and normalization (fallback)
     * @param pcmBuffer - Raw PCM audio buffer
     * @returns Enhanced PCM audio buffer
     */
    static enhanceAudioQuality(pcmBuffer) {
        try {
            // Convert to float32 for processing
            const samples = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < samples.length; i++) {
                samples[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            // Apply noise gate (remove very quiet samples that are likely noise)
            const noiseThreshold = 0.01; // Adjust based on testing
            for (let i = 0; i < samples.length; i++) {
                if (Math.abs(samples[i]) < noiseThreshold) {
                    samples[i] *= 0.1; // Reduce noise rather than eliminate completely
                }
            }

            // Apply simple high-pass filter to remove low-frequency noise
            const filteredSamples = AudioProcessor.applyHighPassFilter(samples, 80, 8000);

            // Normalize audio levels
            const normalizedSamples = AudioProcessor.normalizeAudio(filteredSamples);

            // Convert back to int16 PCM
            const enhancedBuffer = Buffer.alloc(pcmBuffer.length);
            for (let i = 0; i < normalizedSamples.length; i++) {
                const sample = Math.max(-1, Math.min(1, normalizedSamples[i]));
                enhancedBuffer.writeInt16LE(Math.round(sample * 32767), i * 2);
            }

            return enhancedBuffer;
        } catch (error) {
            console.error('❌ Error in audio enhancement:', error);
            return pcmBuffer; // Return original if enhancement fails
        }
    }

    /**
     * Apply simple high-pass filter to remove low-frequency noise
     * @param samples - Float32Array of audio samples
     * @param cutoffFreq - Cutoff frequency in Hz
     * @param sampleRate - Sample rate in Hz
     * @returns Filtered audio samples
     */
    static applyHighPassFilter(samples, cutoffFreq, sampleRate) {
        const rc = 1.0 / (cutoffFreq * 2 * Math.PI);
        const dt = 1.0 / sampleRate;
        const alpha = rc / (rc + dt);

        const filtered = new Float32Array(samples.length);
        filtered[0] = samples[0];

        for (let i = 1; i < samples.length; i++) {
            filtered[i] = alpha * (filtered[i-1] + samples[i] - samples[i-1]);
        }

        return filtered;
    }

    /**
     * Normalize audio levels to prevent clipping and improve consistency
     * @param samples - Float32Array of audio samples
     * @returns Normalized audio samples
     */
    static normalizeAudio(samples) {
        // Find peak amplitude
        let peak = 0;
        for (let i = 0; i < samples.length; i++) {
            peak = Math.max(peak, Math.abs(samples[i]));
        }

        // Avoid division by zero and over-normalization
        if (peak < 0.001) return samples;

        // Apply gentle normalization (don't normalize to full scale to avoid harshness)
        const targetLevel = 0.7;
        const gain = Math.min(targetLevel / peak, 3.0); // Limit gain to prevent over-amplification

        const normalized = new Float32Array(samples.length);
        for (let i = 0; i < samples.length; i++) {
            normalized[i] = samples[i] * gain;
        }

        return normalized;
    }

    /**
     * Converts PCM audio to Float32Array format for Gemini Live API
     * Enhanced with quality improvements
     * @param pcmBuffer - PCM audio buffer
     * @returns Float32Array suitable for Gemini
     */
    static pcmToFloat32Array(pcmBuffer) {
        try {
            // Use enhanced PCM buffer if available
            const enhancedBuffer = AudioProcessor.enhanceAudioQuality(pcmBuffer);

            const float32Array = new Float32Array(enhancedBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                // Convert int16 to float32 (-1 to 1 range)
                float32Array[i] = enhancedBuffer.readInt16LE(i * 2) / 32768.0;
            }
            return float32Array;
        } catch (error) {
            console.error('❌ Error in PCM to Float32 conversion:', error);
            // Fallback to basic conversion
            const float32Array = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }
            return float32Array;
        }
    }

    /**
     * Enhanced resampling from 8kHz to 16kHz using cubic interpolation
     * @param data - Float32Array audio data at 8kHz
     * @returns Float32Array audio data at 16kHz
     */
    static upsample8kTo16k(data) {
        try {
            // Use cubic interpolation for better quality
            return AudioProcessor.resampleCubic(data, 2.0);
        } catch (error) {
            console.error('❌ Error in enhanced upsampling, falling back to linear:', error);
            return AudioProcessor.upsample8kTo16kLinear(data);
        }
    }

    /**
     * Fallback linear interpolation resampling from 8kHz to 16kHz
     * @param data - Float32Array audio data at 8kHz
     * @returns Float32Array audio data at 16kHz
     */
    static upsample8kTo16kLinear(data) {
        const outputLength = data.length * 2; // Double the sample rate
        const output = new Float32Array(outputLength);

        for (let i = 0; i < data.length - 1; i++) {
            const current = data[i];
            const next = data[i + 1];

            // Original sample
            output[i * 2] = current;
            // Interpolated sample
            output[i * 2 + 1] = (current + next) / 2;
        }

        // Handle last sample
        if (data.length > 0) {
            output[outputLength - 2] = data[data.length - 1];
            output[outputLength - 1] = data[data.length - 1];
        }

        return output;
    }

    /**
     * Cubic interpolation resampling for better audio quality
     * @param input - Input audio data
     * @param ratio - Resampling ratio (output_rate / input_rate)
     * @returns Resampled audio data
     */
    static resampleCubic(input, ratio) {
        const outputLength = Math.floor(input.length * ratio);
        const output = new Float32Array(outputLength);

        for (let i = 0; i < outputLength; i++) {
            const srcIndex = i / ratio;
            const srcIndexInt = Math.floor(srcIndex);
            const fraction = srcIndex - srcIndexInt;

            // Get 4 points for cubic interpolation
            const y0 = input[Math.max(0, srcIndexInt - 1)] || 0;
            const y1 = input[srcIndexInt] || 0;
            const y2 = input[Math.min(input.length - 1, srcIndexInt + 1)] || 0;
            const y3 = input[Math.min(input.length - 1, srcIndexInt + 2)] || 0;

            // Cubic interpolation
            const a = -0.5 * y0 + 1.5 * y1 - 1.5 * y2 + 0.5 * y3;
            const b = y0 - 2.5 * y1 + 2 * y2 - 0.5 * y3;
            const c = -0.5 * y0 + 0.5 * y2;
            const d = y1;

            output[i] = a * fraction * fraction * fraction + b * fraction * fraction + c * fraction + d;
        }

        return output;
    }

    /**
     * Enhanced downsampling from 24kHz to 8kHz with anti-aliasing
     * @param data - Float32Array audio data at 24kHz
     * @returns Float32Array audio data at 8kHz
     */
    static downsample24kTo8k(data) {
        try {
            // Apply anti-aliasing filter before downsampling
            const filtered = AudioProcessor.applyAntiAliasingFilter(data, 24000, 4000);
            return AudioProcessor.resampleCubic(filtered, 8000 / 24000);
        } catch (error) {
            console.error('❌ Error in enhanced downsampling, falling back to simple:', error);
            return AudioProcessor.downsample24kTo8kSimple(data);
        }
    }

    /**
     * Simple decimation resampling from 24kHz to 8kHz (fallback)
     * @param data - Float32Array audio data at 24kHz
     * @returns Float32Array audio data at 8kHz
     */
    static downsample24kTo8kSimple(data) {
        const outputLength = Math.floor(data.length / 3); // Divide by 3 (24k/8k = 3)
        const output = new Float32Array(outputLength);

        for (let i = 0; i < outputLength; i++) {
            // Take every 3rd sample (simple decimation)
            output[i] = data[i * 3];
        }

        return output;
    }

    /**
     * Apply anti-aliasing low-pass filter before downsampling
     * @param samples - Input audio samples
     * @param sampleRate - Input sample rate
     * @param cutoffFreq - Cutoff frequency for anti-aliasing
     * @returns Filtered audio samples
     */
    static applyAntiAliasingFilter(samples, sampleRate, cutoffFreq) {
        const rc = 1.0 / (cutoffFreq * 2 * Math.PI);
        const dt = 1.0 / sampleRate;
        const alpha = dt / (rc + dt);

        const filtered = new Float32Array(samples.length);
        filtered[0] = samples[0];

        for (let i = 1; i < samples.length; i++) {
            filtered[i] = filtered[i-1] + alpha * (samples[i] - filtered[i-1]);
        }

        return filtered;
    }

    /**
     * Creates audio blob for Gemini API (based on template)
     * @param data - Float32Array audio data at 8kHz
     * @returns Audio blob for Gemini at 16kHz
     */
    static createGeminiAudioBlob(data) {
        // Upsample from 8kHz to 16kHz for Gemini
        const upsampled = AudioProcessor.upsample8kTo16k(data);

        const int16 = new Int16Array(upsampled.length);
        for (let i = 0; i < upsampled.length; i++) {
            // Convert float32 -1 to 1 to int16 -32768 to 32767
            int16[i] = upsampled[i] * 32768;
        }

        return {
            data: Buffer.from(int16.buffer).toString('base64'),
            mimeType: 'audio/pcm;rate=16000' // Gemini expects 16kHz for input
        };
    }

    /**
     * Enhanced conversion of Gemini PCM audio back to μ-law for Twilio
     * @param base64Audio - Base64 encoded PCM audio from Gemini (24kHz)
     * @returns Base64 encoded μ-law audio for Twilio (8kHz)
     */
    static convertPCMToUlaw(base64Audio) {
        try {
            const pcmBuffer = Buffer.from(base64Audio, 'base64');

            // Convert PCM buffer to Float32Array for processing
            const float32Array = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            // Apply audio enhancement before downsampling
            const enhanced = AudioProcessor.enhanceGeminiOutput(float32Array);

            // Downsample from 24kHz to 8kHz with quality preservation
            const downsampled = AudioProcessor.downsample24kTo8k(enhanced);

            // Apply final processing and convert to μ-law
            const processed = AudioProcessor.prepareForTwilio(downsampled);
            const ulawBuffer = Buffer.alloc(processed.length);

            for (let i = 0; i < processed.length; i++) {
                // Ensure proper range and convert to μ-law
                const pcmSample = Math.max(-32767, Math.min(32767, Math.round(processed[i] * 32767)));
                ulawBuffer[i] = AudioProcessor.linearToUlaw(pcmSample);
            }

            return ulawBuffer.toString('base64');
        } catch (error) {
            console.error('❌ Error converting PCM to μ-law:', error);
            return AudioProcessor.fallbackPCMToUlaw(base64Audio);
        }
    }

    /**
     * Enhance Gemini output audio quality using advanced processing
     * @param samples - Float32Array of audio samples from Gemini
     * @returns Enhanced audio samples
     */
    static enhanceGeminiOutput(samples) {
        try {
            // Use advanced audio enhancer for Gemini output
            const enhancementOptions = {
                noiseReduction: true,
                compression: true,
                agc: true,
                voiceEnhancement: true,
                deEssing: AudioProcessor.audioEnhancer.isDeEssingEnabled() // Use configurable de-essing
            };

            const enhanced = AudioProcessor.audioEnhancer.enhance(samples, enhancementOptions);

            // Apply additional Gemini-specific processing
            const compressed = AudioProcessor.applyCompression(enhanced);

            // Apply de-essing only if enabled (for backward compatibility with existing method)
            const deEssed = AudioProcessor.audioEnhancer.isDeEssingEnabled()
                ? AudioProcessor.applyDeEssing(compressed)
                : compressed;

            // Final normalization
            return AudioProcessor.normalizeAudio(deEssed);
        } catch (error) {
            console.error('❌ Error in advanced Gemini output enhancement:', error);
            // Fallback to basic enhancement
            return AudioProcessor.enhanceGeminiOutputBasic(samples);
        }
    }

    /**
     * Basic Gemini output enhancement (fallback)
     * @param samples - Float32Array of audio samples from Gemini
     * @returns Enhanced audio samples
     */
    static enhanceGeminiOutputBasic(samples) {
        try {
            // Apply gentle compression to even out levels
            const compressed = AudioProcessor.applyCompression(samples);

            // Apply de-essing only if enabled
            const deEssed = AudioProcessor.audioEnhancer.isDeEssingEnabled()
                ? AudioProcessor.applyDeEssing(compressed)
                : compressed;

            // Final normalization
            return AudioProcessor.normalizeAudio(deEssed);
        } catch (error) {
            console.error('❌ Error in basic Gemini output enhancement:', error);
            return samples;
        }
    }

    /**
     * Apply gentle compression to even out audio levels
     * @param samples - Input audio samples
     * @returns Compressed audio samples
     */
    static applyCompression(samples) {
        const threshold = 0.6;
        const ratio = 3.0;
        const compressed = new Float32Array(samples.length);

        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            const absample = Math.abs(sample);

            if (absample > threshold) {
                const excess = absample - threshold;
                const compressedExcess = excess / ratio;
                const newLevel = threshold + compressedExcess;
                compressed[i] = sample >= 0 ? newLevel : -newLevel;
            } else {
                compressed[i] = sample;
            }
        }

        return compressed;
    }

    /**
     * Apply de-essing to reduce harsh sibilant sounds
     * @param samples - Input audio samples
     * @returns De-essed audio samples
     */
    static applyDeEssing(samples) {
        // Simple de-essing using frequency-selective compression
        const deEssed = new Float32Array(samples.length);
        const lookAhead = 5; // samples

        for (let i = 0; i < samples.length; i++) {
            let highFreqEnergy = 0;

            // Calculate high-frequency energy in a small window
            for (let j = Math.max(0, i - lookAhead); j < Math.min(samples.length, i + lookAhead); j++) {
                if (j > 0) {
                    const diff = samples[j] - samples[j - 1];
                    highFreqEnergy += diff * diff;
                }
            }

            // Apply reduction if high-frequency energy is excessive
            const threshold = 0.1;
            if (highFreqEnergy > threshold) {
                const reduction = Math.min(0.7, threshold / highFreqEnergy);
                deEssed[i] = samples[i] * reduction;
            } else {
                deEssed[i] = samples[i];
            }
        }

        return deEssed;
    }

    /**
     * Prepare audio for Twilio transmission
     * @param samples - Input audio samples
     * @returns Processed audio samples optimized for Twilio
     */
    static prepareForTwilio(samples) {
        // Apply final EQ to optimize for phone transmission
        const eqed = AudioProcessor.applyPhoneEQ(samples);

        // Apply gentle limiting to prevent clipping
        return AudioProcessor.applyLimiter(eqed);
    }

    /**
     * Apply EQ optimized for phone transmission
     * @param samples - Input audio samples
     * @returns EQ'd audio samples
     */
    static applyPhoneEQ(samples) {
        // Simple EQ boost in the 1-3kHz range for better intelligibility
        const eqed = new Float32Array(samples.length);
        const boost = 1.2; // 20% boost

        // This is a simplified EQ - in production, you'd use proper filter design
        for (let i = 1; i < samples.length - 1; i++) {
            // Simple high-mid boost
            const highMid = (samples[i] - (samples[i-1] + samples[i+1]) / 2) * boost;
            eqed[i] = samples[i] + highMid * 0.3;
        }

        // Handle edges
        eqed[0] = samples[0];
        if (samples.length > 1) eqed[samples.length - 1] = samples[samples.length - 1];

        return eqed;
    }

    /**
     * Apply gentle limiting to prevent clipping
     * @param samples - Input audio samples
     * @returns Limited audio samples
     */
    static applyLimiter(samples) {
        const limit = 0.95;
        const limited = new Float32Array(samples.length);

        for (let i = 0; i < samples.length; i++) {
            const sample = samples[i];
            if (Math.abs(sample) > limit) {
                // Soft limiting
                limited[i] = sample >= 0 ? limit : -limit;
            } else {
                limited[i] = sample;
            }
        }

        return limited;
    }

    /**
     * Fallback PCM to μ-law conversion
     * @param base64Audio - Base64 encoded PCM audio
     * @returns Base64 encoded μ-law audio
     */
    static fallbackPCMToUlaw(base64Audio) {
        try {
            const pcmBuffer = Buffer.from(base64Audio, 'base64');
            const float32Array = new Float32Array(pcmBuffer.length / 2);

            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            const downsampled = AudioProcessor.downsample24kTo8kSimple(float32Array);
            const ulawBuffer = Buffer.alloc(downsampled.length);

            for (let i = 0; i < downsampled.length; i++) {
                const pcmSample = Math.round(downsampled[i] * 32767);
                ulawBuffer[i] = AudioProcessor.linearToUlaw(pcmSample);
            }

            return ulawBuffer.toString('base64');
        } catch (error) {
            console.error('❌ Error in fallback PCM to μ-law conversion:', error);
            return base64Audio;
        }
    }

    /**
     * Converts linear PCM sample to μ-law
     * @param pcm - Linear PCM sample
     * @returns μ-law encoded sample
     */
    static linearToUlaw(pcm) {
        const BIAS = 0x84;
        const CLIP = 32635;

        let sign = (pcm >> 8) & 0x80;
        if (sign !== 0) pcm = -pcm;
        if (pcm > CLIP) pcm = CLIP;

        pcm += BIAS;
        let exponent = 7;
        let expMask = 0x4000;

        for (let i = 0; i < 8; i++) {
            if ((pcm & expMask) !== 0) break;
            exponent--;
            expMask >>= 1;
        }

        const mantissa = (pcm >> (exponent + 3)) & 0x0F;
        const ulaw = ~(sign | (exponent << 4) | mantissa);

        return ulaw & 0xFF;
    }

    /**
     * Audio Quality Monitor - tracks and logs audio quality metrics
     */
    static audioQualityMonitor = {
        metrics: {
            totalSamples: 0,
            clippedSamples: 0,
            silentSamples: 0,
            peakLevel: 0,
            averageLevel: 0,
            dynamicRange: 0,
            lastUpdate: Date.now()
        },

        /**
         * Analyze audio quality metrics
         * @param samples - Float32Array of audio samples
         * @param label - Label for logging (e.g., 'input', 'output')
         */
        analyze(samples, label = 'unknown') {
            try {
                const metrics = this.calculateMetrics(samples);
                this.updateGlobalMetrics(metrics);

                if (process.env.AUDIO_DEBUG === 'true') {
                    console.log(`🎵 [${label}] Audio Quality Metrics:`, {
                        samples: samples.length,
                        peak: metrics.peak.toFixed(3),
                        rms: metrics.rms.toFixed(3),
                        clipping: metrics.clippingPercentage.toFixed(1) + '%',
                        silence: metrics.silencePercentage.toFixed(1) + '%',
                        dynamicRange: metrics.dynamicRange.toFixed(1) + 'dB'
                    });
                }

                // Warn about quality issues
                if (metrics.clippingPercentage > 5) {
                    console.warn(`⚠️ [${label}] High clipping detected: ${metrics.clippingPercentage.toFixed(1)}%`);
                }
                if (metrics.silencePercentage > 80) {
                    console.warn(`⚠️ [${label}] Mostly silent audio: ${metrics.silencePercentage.toFixed(1)}%`);
                }
                if (metrics.dynamicRange < 20) {
                    console.warn(`⚠️ [${label}] Low dynamic range: ${metrics.dynamicRange.toFixed(1)}dB`);
                }

                return metrics;
            } catch (error) {
                console.error(`❌ Error analyzing audio quality for ${label}:`, error);
                return null;
            }
        },

        /**
         * Calculate detailed audio metrics
         * @param samples - Float32Array of audio samples
         * @returns Object with audio quality metrics
         */
        calculateMetrics(samples) {
            let peak = 0;
            let rmsSum = 0;
            let clippedCount = 0;
            let silentCount = 0;
            let min = Infinity;
            let max = -Infinity;

            const silenceThreshold = 0.001;
            const clippingThreshold = 0.95;

            for (let i = 0; i < samples.length; i++) {
                const sample = samples[i];
                const absSample = Math.abs(sample);

                // Peak detection
                if (absSample > peak) peak = absSample;

                // RMS calculation
                rmsSum += sample * sample;

                // Clipping detection
                if (absSample > clippingThreshold) clippedCount++;

                // Silence detection
                if (absSample < silenceThreshold) silentCount++;

                // Min/max for dynamic range
                if (sample < min) min = sample;
                if (sample > max) max = sample;
            }

            const rms = Math.sqrt(rmsSum / samples.length);
            const dynamicRange = 20 * Math.log10((max - min) / 2);

            return {
                peak,
                rms,
                clippingPercentage: (clippedCount / samples.length) * 100,
                silencePercentage: (silentCount / samples.length) * 100,
                dynamicRange: isFinite(dynamicRange) ? dynamicRange : 0,
                sampleCount: samples.length
            };
        },

        /**
         * Update global metrics tracking
         * @param metrics - Current metrics object
         */
        updateGlobalMetrics(metrics) {
            this.metrics.totalSamples += metrics.sampleCount;
            this.metrics.clippedSamples += (metrics.clippingPercentage / 100) * metrics.sampleCount;
            this.metrics.silentSamples += (metrics.silencePercentage / 100) * metrics.sampleCount;

            if (metrics.peak > this.metrics.peakLevel) {
                this.metrics.peakLevel = metrics.peak;
            }

            // Running average of RMS level
            const alpha = 0.1; // Smoothing factor
            this.metrics.averageLevel = this.metrics.averageLevel * (1 - alpha) + metrics.rms * alpha;

            this.metrics.dynamicRange = metrics.dynamicRange;
            this.metrics.lastUpdate = Date.now();
        },

        /**
         * Get current quality summary
         * @returns Object with quality summary
         */
        getSummary() {
            const totalClippingPercentage = this.metrics.totalSamples > 0 ?
                (this.metrics.clippedSamples / this.metrics.totalSamples) * 100 : 0;
            const totalSilencePercentage = this.metrics.totalSamples > 0 ?
                (this.metrics.silentSamples / this.metrics.totalSamples) * 100 : 0;

            return {
                totalSamples: this.metrics.totalSamples,
                peakLevel: this.metrics.peakLevel.toFixed(3),
                averageLevel: this.metrics.averageLevel.toFixed(3),
                clippingPercentage: totalClippingPercentage.toFixed(2) + '%',
                silencePercentage: totalSilencePercentage.toFixed(2) + '%',
                dynamicRange: this.metrics.dynamicRange.toFixed(1) + 'dB',
                lastUpdate: new Date(this.metrics.lastUpdate).toISOString()
            };
        },

        /**
         * Reset metrics
         */
        reset() {
            this.metrics = {
                totalSamples: 0,
                clippedSamples: 0,
                silentSamples: 0,
                peakLevel: 0,
                averageLevel: 0,
                dynamicRange: 0,
                lastUpdate: Date.now()
            };
        }
    };

    /**
     * Debug helper to save audio samples to file for analysis
     * @param samples - Float32Array of audio samples
     * @param filename - Filename to save (without extension)
     * @param sampleRate - Sample rate of the audio
     */
    static async saveAudioDebug(samples, filename, sampleRate = 8000) {
        if (process.env.AUDIO_DEBUG !== 'true') return;

        try {
            const fs = await import('fs');
            const path = await import('path');

            // Convert to 16-bit PCM
            const pcmBuffer = Buffer.alloc(samples.length * 2);
            for (let i = 0; i < samples.length; i++) {
                const sample = Math.max(-1, Math.min(1, samples[i]));
                pcmBuffer.writeInt16LE(Math.round(sample * 32767), i * 2);
            }

            // Create debug directory if it doesn't exist
            const debugDir = path.join(process.cwd(), 'audio-debug');
            if (!fs.existsSync(debugDir)) {
                fs.mkdirSync(debugDir, { recursive: true });
            }

            // Save raw PCM file
            const filepath = path.join(debugDir, `${filename}_${Date.now()}.pcm`);
            fs.writeFileSync(filepath, pcmBuffer);

            console.log(`🎵 Debug: Saved audio to ${filepath} (${samples.length} samples, ${sampleRate}Hz)`);
        } catch (error) {
            console.error('❌ Error saving audio debug file:', error);
        }
    }
}

// Initialize Gemini client
let geminiClient = null;
try {
    geminiClient = new GoogleGenAI({
        apiKey: GEMINI_API_KEY
    });
    console.log('🤖 Gemini client initialized successfully');
} catch (error) {
    console.error('❌ Error initializing Gemini client:', error);
}

// Map to store active WebSocket connections keyed by callSid
const activeConnections = new Map();

// Context Storage and Recovery System
class ContextManager {
    constructor() {
        this.contextStore = new Map(); // In-memory storage for session contexts
        this.recoveryAttempts = new Map(); // Track recovery attempts per session
        this.maxRecoveryAttempts = 3;
        this.contextSaveInterval = 5000; // Save context every 5 seconds
    }

    // Save session context for recovery
    saveSessionContext(callSid, context) {
        const timestamp = Date.now();
        const sessionContext = {
            callSid,
            timestamp,
            sessionConfig: {
                aiInstructions: context.aiInstructions,
                voice: context.voice,
                model: context.model,
                targetName: context.targetName,
                targetPhoneNumber: context.targetPhoneNumber,
                isIncomingCall: context.isIncomingCall
            },
            conversationState: {
                isSessionActive: context.isSessionActive,
                summaryRequested: context.summaryRequested,
                summaryText: context.summaryText || "",
                conversationLog: context.conversationLog || []
            },
            connectionState: {
                streamSid: context.streamSid,
                lastActivity: timestamp,
                audioBufferState: context.audioBufferState || null
            },
            recoveryInfo: {
                lastRecoveryTime: null,
                recoveryCount: this.recoveryAttempts.get(callSid) || 0,
                wasInterrupted: false
            }
        };

        this.contextStore.set(callSid, sessionContext);
        console.log(`💾 [${callSid}] Session context saved at ${new Date(timestamp).toISOString()}`);
        return sessionContext;
    }

    // Retrieve session context for recovery
    getSessionContext(callSid) {
        return this.contextStore.get(callSid);
    }

    // Mark session as interrupted and needing recovery
    markSessionInterrupted(callSid, reason = 'unknown') {
        const context = this.contextStore.get(callSid);
        if (context) {
            context.recoveryInfo.wasInterrupted = true;
            context.recoveryInfo.interruptionReason = reason;
            context.recoveryInfo.interruptionTime = Date.now();
            this.contextStore.set(callSid, context);
            console.log(`⚠️ [${callSid}] Session marked as interrupted: ${reason}`);
        }
    }

    // Check if session can be recovered
    canRecover(callSid) {
        const context = this.contextStore.get(callSid);
        if (!context) return false;

        const recoveryCount = this.recoveryAttempts.get(callSid) || 0;
        const timeSinceLastActivity = Date.now() - context.connectionState.lastActivity;
        const maxRecoveryTime = 300000; // 5 minutes

        return recoveryCount < this.maxRecoveryAttempts &&
               timeSinceLastActivity < maxRecoveryTime;
    }

    // Increment recovery attempt counter
    incrementRecoveryAttempt(callSid) {
        const current = this.recoveryAttempts.get(callSid) || 0;
        this.recoveryAttempts.set(callSid, current + 1);
        return current + 1;
    }

    // Clean up old contexts
    cleanupOldContexts() {
        const now = Date.now();
        const maxAge = 3600000; // 1 hour

        for (const [callSid, context] of this.contextStore.entries()) {
            if (now - context.timestamp > maxAge) {
                this.contextStore.delete(callSid);
                this.recoveryAttempts.delete(callSid);
                console.log(`🧹 [${callSid}] Cleaned up old session context`);
            }
        }
    }

    // Get comprehensive recovery message with full conversation history
    getRecoveryMessage(callSid) {
        const context = this.contextStore.get(callSid);
        if (!context || !context.recoveryInfo.wasInterrupted) return null;

        const recoveryCount = context.recoveryInfo.recoveryCount;
        const interruptionReason = context.recoveryInfo.interruptionReason || 'connection issue';

        // Build complete conversation history for full context restoration
        let fullConversationHistory = '';
        if (context.conversationState?.fullTranscript && context.conversationState.fullTranscript.length > 0) {
            const conversationLog = context.conversationState.fullTranscript.map(msg => {
                const speaker = msg.role === 'assistant' ? 'AI Assistant' : 'Customer';
                const timestamp = new Date(msg.timestamp).toLocaleTimeString();
                return `[${timestamp}] ${speaker}: ${msg.content}`;
            }).join('\n');

            fullConversationHistory = `\n\nCOMPLETE CONVERSATION HISTORY:\n${conversationLog}\n`;
        }

        // Include original AI instructions from campaign script
        const originalInstructions = context.sessionConfig?.aiInstructions || '';

        return `${originalInstructions}

[RECOVERY NOTICE]
You were briefly disconnected during the conversation. This is recovery attempt ${recoveryCount}.
You must continue the conversation seamlessly from exactly where it was interrupted.
${fullConversationHistory}
CRITICAL INSTRUCTIONS:
1. Resume the conversation naturally without mentioning any technical issues
2. Continue from the last point in the conversation above
3. Maintain the same tone and context as before the interruption
4. The customer should not notice any disruption in service
5. Do NOT acknowledge this recovery message to the customer

Continue the conversation now:`;
    }

    // Clear session context
    clearSessionContext(callSid) {
        this.contextStore.delete(callSid);
        this.recoveryAttempts.delete(callSid);
        console.log(`🗑️ [${callSid}] Session context cleared`);
    }
}

// Initialize context manager
const contextManager = new ContextManager();

// Cleanup old contexts every 10 minutes
setInterval(() => {
    contextManager.cleanupOldContexts();
}, 600000);

// Connection Health Monitoring System
class ConnectionHealthMonitor {
    constructor() {
        this.connectionStates = new Map(); // Track connection states per callSid
        this.healthMetrics = {
            totalConnections: 0,
            activeConnections: 0,
            failedConnections: 0,
            recoveredConnections: 0,
            lastHealthCheck: Date.now()
        };
        this.healthCheckInterval = 30000; // Check every 30 seconds
        this.connectionTimeout = 10000; // 10 seconds timeout for connection establishment
        this.startHealthMonitoring();
    }

    // Track connection state for a session
    trackConnection(callSid, state, details = {}) {
        const timestamp = Date.now();
        const connectionState = {
            callSid,
            state, // 'connecting', 'connected', 'disconnected', 'failed', 'recovering'
            timestamp,
            details,
            lastPing: null,
            consecutiveFailures: this.connectionStates.get(callSid)?.consecutiveFailures || 0
        };

        if (state === 'failed' || state === 'disconnected') {
            connectionState.consecutiveFailures++;
        } else if (state === 'connected') {
            connectionState.consecutiveFailures = 0;
        }

        this.connectionStates.set(callSid, connectionState);
        this.updateHealthMetrics(state);

        console.log(`🔍 [${callSid}] Connection state: ${state} (failures: ${connectionState.consecutiveFailures})`);

        // Trigger recovery if needed
        if (this.shouldTriggerRecovery(callSid, connectionState)) {
            this.triggerRecovery(callSid, 'connection_failure');
        }
    }

    // Update overall health metrics
    updateHealthMetrics(state) {
        switch (state) {
            case 'connecting':
                this.healthMetrics.totalConnections++;
                break;
            case 'connected':
                this.healthMetrics.activeConnections++;
                break;
            case 'disconnected':
            case 'failed':
                this.healthMetrics.activeConnections = Math.max(0, this.healthMetrics.activeConnections - 1);
                if (state === 'failed') {
                    this.healthMetrics.failedConnections++;
                }
                break;
            case 'recovered':
                this.healthMetrics.recoveredConnections++;
                break;
        }
        this.healthMetrics.lastHealthCheck = Date.now();
    }

    // Check if recovery should be triggered
    shouldTriggerRecovery(callSid, connectionState) {
        const maxFailures = 2;
        const timeSinceLastFailure = Date.now() - connectionState.timestamp;
        const recentFailure = timeSinceLastFailure < 60000; // Within last minute

        return connectionState.consecutiveFailures >= maxFailures &&
               recentFailure &&
               contextManager.canRecover(callSid);
    }

    // Trigger recovery for a session
    triggerRecovery(callSid, reason) {
        console.log(`🔄 [${callSid}] Triggering recovery due to: ${reason}`);
        contextManager.markSessionInterrupted(callSid, reason);

        // Emit recovery event for session recovery mechanism
        this.emit('recovery_needed', { callSid, reason });
    }

    // Ping connection to check health
    pingConnection(callSid, geminiSession) {
        if (!geminiSession) return false;

        try {
            // Update last ping time
            const connectionState = this.connectionStates.get(callSid);
            if (connectionState) {
                connectionState.lastPing = Date.now();
                this.connectionStates.set(callSid, connectionState);
            }

            // For Gemini Live API, we can send a minimal message to test connectivity
            // This is a lightweight ping that doesn't affect the conversation
            return true;
        } catch (error) {
            console.error(`❌ [${callSid}] Connection ping failed:`, error);
            this.trackConnection(callSid, 'failed', { error: error.message });
            return false;
        }
    }

    // Start periodic health monitoring
    startHealthMonitoring() {
        setInterval(() => {
            this.performHealthCheck();
        }, this.healthCheckInterval);

        console.log(`🏥 Connection health monitoring started (interval: ${this.healthCheckInterval}ms)`);
    }

    // Perform comprehensive health check
    performHealthCheck() {
        const now = Date.now();
        let healthyConnections = 0;
        let unhealthyConnections = 0;

        for (const [callSid, connectionState] of this.connectionStates.entries()) {
            const timeSinceLastActivity = now - connectionState.timestamp;
            const isStale = timeSinceLastActivity > 300000; // 5 minutes

            if (isStale) {
                // Clean up stale connections
                this.connectionStates.delete(callSid);
                console.log(`🧹 [${callSid}] Removed stale connection state`);
                continue;
            }

            if (connectionState.state === 'connected' && connectionState.consecutiveFailures === 0) {
                healthyConnections++;
            } else {
                unhealthyConnections++;
            }
        }

        // Update metrics
        this.healthMetrics.activeConnections = healthyConnections;
        this.healthMetrics.lastHealthCheck = now;

        // Log health summary
        if (healthyConnections > 0 || unhealthyConnections > 0) {
            console.log(`🏥 Health check: ${healthyConnections} healthy, ${unhealthyConnections} unhealthy connections`);
        }
    }

    // Get current health status
    getHealthStatus() {
        return {
            ...this.healthMetrics,
            connectionStates: Array.from(this.connectionStates.values()),
            timestamp: Date.now()
        };
    }

    // Simple event emitter for recovery events
    emit(event, data) {
        if (event === 'recovery_needed') {
            // Handle recovery event immediately
            setTimeout(() => {
                this.handleRecoveryEvent(data);
            }, 100);
        }
    }

    // Handle recovery event
    handleRecoveryEvent(data) {
        const { callSid, reason } = data;
        console.log(`🚨 [${callSid}] Recovery event triggered: ${reason}`);

        // This will be handled by the session recovery mechanism
        // For now, just log and update state
        this.trackConnection(callSid, 'recovering', { reason });
    }

    // Clean up connection tracking
    removeConnection(callSid) {
        this.connectionStates.delete(callSid);
        console.log(`🗑️ [${callSid}] Connection health tracking removed`);
    }
}

// Initialize connection health monitor
const healthMonitor = new ConnectionHealthMonitor();

// Session Recovery Mechanism
class SessionRecoveryManager {
    constructor(contextManager, healthMonitor) {
        this.contextManager = contextManager;
        this.healthMonitor = healthMonitor;
        this.recoveryQueue = new Map(); // Queue of sessions awaiting recovery
        this.recoveryInProgress = new Set(); // Track sessions currently being recovered
        this.maxRecoveryTime = 30000; // 30 seconds max recovery time
    }

    // Attempt to recover a session
    async recoverSession(callSid, reason = 'unknown') {
        if (this.recoveryInProgress.has(callSid)) {
            console.log(`⏳ [${callSid}] Recovery already in progress, skipping`);
            return false;
        }

        const context = this.contextManager.getSessionContext(callSid);
        if (!context || !this.contextManager.canRecover(callSid)) {
            console.log(`❌ [${callSid}] Cannot recover session - no context or max attempts reached`);
            return false;
        }

        this.recoveryInProgress.add(callSid);
        const recoveryAttempt = this.contextManager.incrementRecoveryAttempt(callSid);

        console.log(`🔄 [${callSid}] Starting session recovery attempt ${recoveryAttempt} (reason: ${reason})`);

        try {
            // Get current connection data
            const connectionData = activeConnections.get(callSid);
            if (!connectionData) {
                console.log(`❌ [${callSid}] No active connection data found for recovery`);
                return false;
            }

            // Mark context as being recovered
            context.recoveryInfo.lastRecoveryTime = Date.now();
            context.recoveryInfo.recoveryCount = recoveryAttempt;
            context.recoveryInfo.wasInterrupted = true;
            context.recoveryInfo.interruptionReason = reason;

            // Attempt to recreate Gemini session with recovery context
            const recoveredSession = await this.recreateGeminiSession(callSid, context, connectionData);

            if (recoveredSession) {
                // Update connection data with recovered session
                connectionData.geminiSession = recoveredSession;

                // Send recovery notification to AI
                await this.sendRecoveryNotification(callSid, recoveredSession, context);

                // Update health monitoring
                this.healthMonitor.trackConnection(callSid, 'recovered', {
                    attempt: recoveryAttempt,
                    reason
                });

                console.log(`✅ [${callSid}] Session recovery successful on attempt ${recoveryAttempt}`);
                return true;
            } else {
                console.log(`❌ [${callSid}] Failed to recreate Gemini session during recovery`);
                return false;
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error during session recovery:`, error);
            this.healthMonitor.trackConnection(callSid, 'failed', {
                error: error.message,
                recoveryAttempt
            });
            return false;
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }

    // Recreate Gemini session with recovery context
    async recreateGeminiSession(callSid, context, connectionData) {
        try {
            const sessionConfig = context.sessionConfig;

            console.log(`🤖 [${callSid}] Recreating Gemini session with model: ${sessionConfig.model}, voice: ${sessionConfig.voice}`);

            // Create new Gemini session with enhanced error handling
            const newGeminiSession = await geminiClient.live.connect({
                model: sessionConfig.model,
                callbacks: {
                    onopen: () => {
                        console.log(`✅ [${callSid}] Recovered Gemini session opened successfully`);
                        this.healthMonitor.trackConnection(callSid, 'connected', { recovered: true });
                    },

                    onerror: (error) => {
                        console.error(`❌ [${callSid}] Recovered Gemini session error:`, error);
                        this.healthMonitor.trackConnection(callSid, 'failed', {
                            error: error.message,
                            recovered: true
                        });

                        // Attempt another recovery if possible
                        if (this.contextManager.canRecover(callSid)) {
                            setTimeout(() => {
                                this.recoverSession(callSid, 'session_error_after_recovery');
                            }, 5000);
                        }
                    },

                    onclose: () => {
                        console.log(`🔌 [${callSid}] Recovered Gemini session closed`);
                        this.healthMonitor.trackConnection(callSid, 'disconnected', { recovered: true });
                    },

                    onmessage: async (message) => {
                        // Use the same message handling logic as the original session
                        // This ensures consistency in audio processing and response handling
                        try {
                            const connectionData = activeConnections.get(callSid);
                            if (!connectionData) {
                                console.warn(`⚠️ [${callSid}] No connection data found for recovered session message handling`);
                                return;
                            }

                            // Handle audio response from Gemini (same as original implementation)
                            if (message?.serverContent?.modelTurn?.parts?.[0]?.inlineData) {
                                const audio = message.serverContent.modelTurn.parts[0].inlineData;

                                if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                    if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
                                        try {
                                            const convertedAudio = AudioProcessor.convertPCMToUlaw(audio.data);
                                            const audioDelta = {
                                                event: 'media',
                                                streamSid: context.connectionState.streamSid,
                                                media: { payload: convertedAudio }
                                            };
                                            connectionData.twilioWs.send(JSON.stringify(audioDelta));
                                            console.log(`🔊 [${callSid}] Sent recovered session audio to Twilio`);
                                        } catch (audioError) {
                                            console.error(`❌ [${callSid}] Error processing recovered session audio:`, audioError);
                                        }
                                    }
                                }
                            }

                            // Handle text responses
                            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log(`💬 [${callSid}] Recovered session response: ${text.substring(0, 100)}...`);
                                if (connectionData.summaryRequested) {
                                    connectionData.summaryText += text;
                                }
                            }

                        } catch (error) {
                            console.error(`❌ [${callSid}] Error processing recovered session message:`, error);
                        }
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: sessionConfig.voice
                            }
                        }
                    },
                    // NO systemInstruction according to CAMPAIGN_SCRIPT_POLICY.md
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192,
                responseMimeType: "text/plain"
            });

            return newGeminiSession;

        } catch (error) {
            console.error(`❌ [${callSid}] Error recreating Gemini session:`, error);
            return null;
        }
    }

    // Send recovery notification to AI
    async sendRecoveryNotification(callSid, geminiSession, context) {
        try {
            const recoveryMessage = this.contextManager.getRecoveryMessage(callSid);
            if (recoveryMessage && geminiSession) {
                console.log(`📢 [${callSid}] Sending recovery notification to AI`);

                geminiSession.sendClientContent({
                    turns: [{
                        role: 'user',
                        parts: [{
                            text: recoveryMessage
                        }]
                    }],
                    turnComplete: true
                });
            }
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending recovery notification:`, error);
        }
    }

    // Check if session needs recovery
    needsRecovery(callSid) {
        const context = this.contextManager.getSessionContext(callSid);
        const connectionData = activeConnections.get(callSid);

        return context &&
               connectionData &&
               (!connectionData.geminiSession || !connectionData.isSessionActive) &&
               this.contextManager.canRecover(callSid);
    }

    // Get recovery status for a session
    getRecoveryStatus(callSid) {
        const context = this.contextManager.getSessionContext(callSid);
        const isRecovering = this.recoveryInProgress.has(callSid);

        return {
            canRecover: this.contextManager.canRecover(callSid),
            isRecovering,
            recoveryAttempts: context?.recoveryInfo?.recoveryCount || 0,
            wasInterrupted: context?.recoveryInfo?.wasInterrupted || false,
            lastRecoveryTime: context?.recoveryInfo?.lastRecoveryTime
        };
    }
}

// Initialize session recovery manager
const recoveryManager = new SessionRecoveryManager(contextManager, healthMonitor);

// Input validation and sanitization utilities
class SecurityUtils {
    /**
     * Validate and sanitize phone number input
     * @param {string} phoneNumber - Phone number to validate
     * @returns {string|null} Sanitized phone number or null if invalid
     */
    static validatePhoneNumber(phoneNumber) {
        if (!phoneNumber || typeof phoneNumber !== 'string') return null;

        // Remove all non-digit characters except + at the beginning
        const sanitized = phoneNumber.replace(/[^\d+]/g, '');

        // Basic validation: should start with + and have 10-15 digits
        if (!/^\+\d{10,15}$/.test(sanitized)) return null;

        return sanitized;
    }

    /**
     * Validate and sanitize text input
     * @param {string} text - Text to validate
     * @param {number} maxLength - Maximum allowed length
     * @returns {string|null} Sanitized text or null if invalid
     */
    static validateText(text, maxLength = 1000) {
        if (!text || typeof text !== 'string') return null;

        // Remove potentially dangerous characters
        const sanitized = text
            .replace(/[<>]/g, '') // Remove HTML tags
            .replace(/javascript:/gi, '') // Remove javascript: protocol
            .replace(/data:/gi, '') // Remove data: protocol
            .trim();

        if (sanitized.length > maxLength) return null;

        return sanitized;
    }

    /**
     * Validate JSON input
     * @param {string} jsonString - JSON string to validate
     * @param {number} maxSize - Maximum size in bytes
     * @returns {Object|null} Parsed JSON or null if invalid
     */
    static validateJSON(jsonString, maxSize = 10240) { // 10KB max
        if (!jsonString || typeof jsonString !== 'string') return null;

        if (jsonString.length > maxSize) return null;

        try {
            const parsed = JSON.parse(jsonString);

            // Additional validation for nested objects
            if (typeof parsed !== 'object' || parsed === null) return null;

            return parsed;
        } catch (error) {
            return null;
        }
    }

    /**
     * Rate limiting check for specific operations
     * @param {string} key - Unique key for rate limiting
     * @param {number} maxRequests - Maximum requests allowed
     * @param {number} windowMs - Time window in milliseconds
     * @returns {boolean} True if request is allowed
     */
    static checkRateLimit(key, maxRequests = 10, windowMs = 60000) {
        if (!SecurityUtils.rateLimitStore) {
            SecurityUtils.rateLimitStore = new Map();
        }

        const now = Date.now();
        const windowStart = now - windowMs;

        // Get or create request history for this key
        let requests = SecurityUtils.rateLimitStore.get(key) || [];

        // Remove old requests outside the window
        requests = requests.filter(timestamp => timestamp > windowStart);

        // Check if limit exceeded
        if (requests.length >= maxRequests) {
            return false;
        }

        // Add current request
        requests.push(now);
        SecurityUtils.rateLimitStore.set(key, requests);

        return true;
    }

    /**
     * Clean up old rate limit entries
     */
    static cleanupRateLimit() {
        if (!SecurityUtils.rateLimitStore) return;

        const now = Date.now();
        const maxAge = 300000; // 5 minutes

        for (const [key, requests] of SecurityUtils.rateLimitStore.entries()) {
            const validRequests = requests.filter(timestamp => now - timestamp < maxAge);
            if (validRequests.length === 0) {
                SecurityUtils.rateLimitStore.delete(key);
            } else {
                SecurityUtils.rateLimitStore.set(key, validRequests);
            }
        }
    }
}

// Clean up rate limit store every 5 minutes
setInterval(() => SecurityUtils.cleanupRateLimit(), 300000);



// Helper function to analyze WebSocket close codes
function analyzeCloseCode(code) {
    const closeCodes = {
        1000: { description: 'Normal closure', isError: false },
        1001: { description: 'Going away (page unload)', isError: false },
        1002: { description: 'Protocol error', isError: true },
        1003: { description: 'Unsupported data type', isError: true },
        1004: { description: 'Reserved', isError: true },
        1005: { description: 'No status code', isError: false },
        1006: { description: 'Abnormal closure', isError: true },
        1007: { description: 'Invalid frame payload data', isError: true },
        1008: { description: 'Policy violation', isError: true },
        1009: { description: 'Message too big', isError: true },
        1010: { description: 'Missing extension', isError: true },
        1011: { description: 'Internal server error', isError: true },
        1012: { description: 'Service restart', isError: false },
        1013: { description: 'Try again later', isError: false },
        1014: { description: 'Bad gateway', isError: true },
        1015: { description: 'TLS handshake failure', isError: true }
    };

    return closeCodes[code] || {
        description: `Unknown close code: ${code}`,
        isError: code >= 1002 && code !== 1005 && code !== 1012 && code !== 1013
    };
}

// Store the configuration for the next call
// According to CAMPAIGN_SCRIPT_POLICY.md: No system messages, only AI instructions from scripts
let nextCallConfig = {
    aiInstructions: null, // Will be populated from campaign script
    voice: DEFAULT_VOICE,
    model: DEFAULT_GEMINI_MODEL,
    targetName: null,
    targetPhoneNumber: null
};

// Initialize Fastify with enhanced configuration for security and performance
const fastify = Fastify({
    logger: {
        level: process.env.LOG_LEVEL || 'info',
        prettyPrint: process.env.NODE_ENV === 'development'
    },
    trustProxy: true, // Enable trust proxy for rate limiting and security headers
    bodyLimit: 1048576, // 1MB body limit for security
    keepAliveTimeout: 30000, // 30 seconds keep-alive
    connectionTimeout: 60000 // 60 seconds connection timeout
});

// Register security middleware
await fastify.register(import('@fastify/helmet'), {
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "wss:", "https:"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
            workerSrc: ["'self'"], // Allow AudioWorklet modules
            childSrc: ["'self'"] // Allow web workers and AudioWorklet
        }
    },
    crossOriginEmbedderPolicy: false // Disable for audio processing compatibility
});

// Register rate limiting for API endpoints
await fastify.register(import('@fastify/rate-limit'), {
    max: 100, // Maximum 100 requests per window
    timeWindow: '1 minute', // Per minute
    skipOnError: true, // Don't count failed requests
    keyGenerator: (request) => {
        // Use IP address for rate limiting
        return request.ip || request.socket.remoteAddress;
    },
    errorResponseBuilder: (request, context) => {
        return {
            code: 429,
            error: 'Too Many Requests',
            message: `Rate limit exceeded, retry in ${Math.round(context.ttl / 1000)} seconds`,
            retryAfter: Math.round(context.ttl / 1000)
        };
    }
});

// Register CORS plugin - Enhanced with security considerations
fastify.register(fastifyCors, {
  origin: (origin, cb) => {
    const allowedOrigins = [
        PUBLIC_URL, // Backend public URL
        process.env.FRONTEND_URL || 'http://localhost:3011', // Frontend URL (adjust port if needed)
        'http://localhost:3001', // Gemini frontend dev server
        'http://localhost:3002', // OpenAI frontend dev server
        'http://localhost:3011', // Gemini frontend production server
        'http://localhost:3012', // OpenAI frontend production server
        'https://www.verduona.com', // Main website
        'https://verduona.com', // Main website without www
        'https://twilio-openai.verduona.com', // Twilio OpenAI frontend
        'https://twilio-gemini.verduona.com', // Twilio Gemini frontend
        'https://gemini-api.verduona.com', // This backend
        process.env.CORS_ORIGIN // Additional CORS origin from env
    ].filter(Boolean).map(url => {
        try {
            return new URL(url).origin;
        } catch (e) {
            console.warn(`⚠️ Invalid URL in CORS origins: ${url}`);
            return null;
        }
    }).filter(Boolean); // Get valid origins only

    // Allow Twilio webhooks (no origin header)
    if (!origin) {
      console.log('🔒 CORS: Allowing request with no origin (likely Twilio webhook)');
      cb(null, true); return;
    }

    try {
        const requestOrigin = new URL(origin).origin;

        // Check if origin is in allowed list
        if (allowedOrigins.includes(requestOrigin)) {
          console.log(`🔒 CORS: Allowing origin: ${origin}`);
          cb(null, true); return;
        }

        // Allow localhost for development
        if (process.env.NODE_ENV === 'development' && new URL(origin).hostname === 'localhost') {
            console.log(`🔒 CORS: Allowing localhost for development: ${origin}`);
            cb(null, true); return;
        }

        // Allow any Twilio domain
        if (origin.includes('twilio.com')) {
          console.log(`🔒 CORS: Allowing Twilio domain: ${origin}`);
          cb(null, true); return;
        }

        // Log and reject all other origins
        console.warn(`🚫 CORS: Rejecting origin: ${origin}`);
        cb(new Error("Not allowed by CORS"), false);
    } catch (e) {
        console.error(`❌ CORS: Invalid origin format: ${origin}`, e);
        cb(new Error("Invalid origin format"), false);
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Twilio-Signature']
});

// Register compression for better performance
fastify.register(import('compression'), {
    threshold: 1024, // Only compress responses larger than 1KB
    level: 6, // Compression level (1-9, 6 is good balance)
    chunkSize: 16384 // 16KB chunks
});

// Register form body parser and WebSocket support
fastify.register(fastifyFormBody);
fastify.register(fastifyWs);

// Register simple Supabase authentication middleware for demo access
fastify.addHook('preHandler', validateSupabaseAuth);

// Static file serving registration
fastify.register(fastifyStatic, {
  root: __dirname,
  prefix: '/static/',
});

// Root route
fastify.get('/', async (request, reply) => {
    return {
        message: 'Twilio Gemini Live API Server is running! 🚀',
        currentModel: CURRENT_GEMINI_MODEL,
        defaultModel: DEFAULT_GEMINI_MODEL,
        voice: DEFAULT_VOICE,
        timestamp: new Date().toISOString()
    };
});

// Outbound call scripts management interface (when app user calls someone else)
fastify.get('/scripts', async (request, reply) => {
    try {
        const html = await readFile(path.join(__dirname, 'outbound-scripts-manager.html'), 'utf8');
        reply.header('Content-Type', 'text/html');
        return html;
    } catch (error) {
        reply.code(500).send({ error: 'Failed to load outbound scripts management interface' });
    }
});

// NEW Incoming call scenarios management interface (separated from outbound)
fastify.get('/incoming', async (request, reply) => {
    try {
        const html = await readFile(path.join(__dirname, 'incoming-manager.html'), 'utf8');
        reply.header('Content-Type', 'text/html');
        return html;
    } catch (error) {
        reply.code(500).send({ error: 'Failed to load incoming call management interface' });
    }
});

// Health check endpoint
fastify.get('/health', async (request, reply) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Twilio Gemini Live API',
        currentModel: CURRENT_GEMINI_MODEL,
        defaultModel: DEFAULT_GEMINI_MODEL,
        voice: DEFAULT_VOICE,
        geminiClientStatus: geminiClient ? 'initialized' : 'error'
    };
    reply.send(health);
});

// Audio settings endpoints
fastify.post('/audio-settings', async (request, reply) => {
    try {
        const { deEssingEnabled, deEssingSettings } = request.body;

        // Update de-essing settings in AudioProcessor
        if (typeof deEssingEnabled === 'boolean') {
            AudioProcessor.audioEnhancer.setDeEssingEnabled(deEssingEnabled);
        }

        if (deEssingSettings && typeof deEssingSettings === 'object') {
            AudioProcessor.audioEnhancer.configureDeEssing(deEssingSettings);
        }

        return {
            success: true,
            deEssingEnabled: AudioProcessor.audioEnhancer.isDeEssingEnabled(),
            message: 'Audio settings updated successfully'
        };
    } catch (error) {
        console.error('❌ Error updating audio settings:', error);
        reply.status(500);
        return { success: false, error: error.message };
    }
});

fastify.get('/audio-settings', async (request, reply) => {
    try {
        return {
            deEssingEnabled: AudioProcessor.audioEnhancer.isDeEssingEnabled(),
            deEssingSettings: AudioProcessor.audioEnhancer.deEssingSettings
        };
    } catch (error) {
        console.error('❌ Error getting audio settings:', error);
        reply.status(500);
        return { success: false, error: error.message };
    }
});

// Provider Health Dashboard API endpoints
fastify.get('/api/provider-health', async (request, reply) => {
    try {
        const healthStatus = healthMonitor.getHealthStatus();
        const contextStats = {
            totalContexts: contextManager.contextStore.size,
            activeRecoveries: recoveryManager.recoveryInProgress.size
        };

        reply.send({
            success: true,
            timestamp: new Date().toISOString(),
            providerHealth: {
                gemini: {
                    status: geminiClient ? 'connected' : 'disconnected',
                    model: CURRENT_GEMINI_MODEL,
                    voice: DEFAULT_VOICE
                }
            },
            connectionHealth: healthStatus,
            contextManager: contextStats,
            activeConnections: activeConnections.size
        });
    } catch (error) {
        console.error('Error getting provider health:', error);
        reply.status(500).send({
            success: false,
            error: 'Failed to get provider health status',
            details: error.message
        });
    }
});

// Session recovery status endpoint
fastify.get('/api/recovery-status/:callSid', async (request, reply) => {
    try {
        const { callSid } = request.params;
        const recoveryStatus = recoveryManager.getRecoveryStatus(callSid);
        const sessionContext = contextManager.getSessionContext(callSid);

        reply.send({
            success: true,
            callSid,
            recoveryStatus,
            hasContext: !!sessionContext,
            contextTimestamp: sessionContext?.timestamp
        });
    } catch (error) {
        console.error('Error getting recovery status:', error);
        reply.status(500).send({
            success: false,
            error: 'Failed to get recovery status',
            details: error.message
        });
    }
});

// Manual recovery trigger endpoint
fastify.post('/api/trigger-recovery/:callSid', async (request, reply) => {
    try {
        const { callSid } = request.params;
        const { reason = 'manual_trigger' } = request.body;

        if (!contextManager.canRecover(callSid)) {
            return reply.status(400).send({
                success: false,
                error: 'Session cannot be recovered',
                reason: 'No context available or max attempts reached'
            });
        }

        const recoveryResult = await recoveryManager.recoverSession(callSid, reason);

        reply.send({
            success: recoveryResult,
            callSid,
            message: recoveryResult ? 'Recovery initiated successfully' : 'Recovery failed',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error triggering manual recovery:', error);
        reply.status(500).send({
            success: false,
            error: 'Failed to trigger recovery',
            details: error.message
        });
    }
});

// Connection health metrics endpoint
fastify.get('/api/connection-metrics', async (request, reply) => {
    try {
        const healthStatus = healthMonitor.getHealthStatus();
        const audioQuality = AudioProcessor.audioQualityMonitor.getSummary();

        reply.send({
            success: true,
            timestamp: new Date().toISOString(),
            metrics: {
                connections: healthStatus,
                audioQuality,
                uptime: process.uptime(),
                memory: process.memoryUsage()
            }
        });
    } catch (error) {
        console.error('Error getting connection metrics:', error);
        reply.status(500).send({
            success: false,
            error: 'Failed to get connection metrics',
            details: error.message
        });
    }
});

// Audio quality metrics endpoint
fastify.get('/api/audio-quality', async (request, reply) => {
    try {
        const qualityMetrics = AudioProcessor.audioQualityMonitor.getSummary();
        reply.send({
            success: true,
            audioQuality: qualityMetrics,
            debugMode: process.env.AUDIO_DEBUG === 'true',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error getting audio quality metrics:', error);
        reply.status(500).send({
            success: false,
            error: 'Failed to get audio quality metrics',
            details: error.message
        });
    }
});

// Reset audio quality metrics endpoint
fastify.post('/api/audio-quality/reset', async (request, reply) => {
    try {
        AudioProcessor.audioQualityMonitor.reset();
        reply.send({
            success: true,
            message: 'Audio quality metrics reset successfully',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error resetting audio quality metrics:', error);
        reply.status(500).send({
            success: false,
            error: 'Failed to reset audio quality metrics',
            details: error.message
        });
    }
});

// Serve incoming campaign JSON files
fastify.get('/incoming-campaign:id.json', async (request, reply) => {
    const campaignId = request.params.id;
    const validCampaignIds = ['1', '2', '3', '4', '5', '6'];
    
    if (!validCampaignIds.includes(campaignId)) {
        reply.status(400).send({ error: 'Invalid incoming campaign ID. Must be 1-6.' });
        return;
    }
    
    try {
        const publicDir = path.join(__dirname, 'call-center-frontend', 'public');
        const filePath = path.join(publicDir, `incoming-campaign${campaignId}.json`);
        const fileContent = await readFile(filePath, 'utf8');
        const jsonData = JSON.parse(fileContent);
        reply.send(jsonData);
    } catch (error) {
        if (error.code === 'ENOENT') {
            reply.status(404).send({ error: `Incoming campaign ${campaignId}.json not found.` });
        } else if (error instanceof SyntaxError) {
            reply.status(400).send({ error: `Incoming campaign ${campaignId}.json contains invalid JSON.` });
        } else {
            reply.status(500).send({ error: 'Failed to read incoming campaign script.' });
        }
        console.error(`❌ Error loading incoming campaign ${campaignId}:`, error);
    }
});

// === OUTBOUND CALL SCRIPTS MANAGEMENT ENDPOINTS ===
// These scripts are used when the app user makes calls to other people

// Get all available outbound call scripts
fastify.get('/api/outbound-scripts', async (request, reply) => {
    try {
        const scripts = listOutboundCallScripts();
        const currentScript = getCurrentOutboundScript();
        reply.send({
            success: true,
            scripts: scripts,
            currentScript: {
                id: currentScript.id,
                name: currentScript.name,
                description: currentScript.description
            }
        });
    } catch (error) {
        console.error('Error listing outbound scripts:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Legacy endpoint for backward compatibility (still called "incoming" but actually outbound)
fastify.get('/api/incoming-scripts', async (request, reply) => {
    try {
        const scripts = listOutboundCallScripts();
        const currentScript = getCurrentOutboundScript();
        reply.send({
            success: true,
            scripts: scripts,
            currentScript: {
                id: currentScript.id,
                name: currentScript.name,
                description: currentScript.description
            }
        });
    } catch (error) {
        console.error('Error listing outbound scripts (legacy endpoint):', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Get current active incoming call script
fastify.get('/api/incoming-scripts/current', async (request, reply) => {
    try {
        const currentScript = getCurrentIncomingScript();
        reply.send({
            success: true,
            script: currentScript
        });
    } catch (error) {
        console.error('Error getting current script:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Set active incoming call script
fastify.post('/api/incoming-scripts/set/:scriptId', async (request, reply) => {
    try {
        const { scriptId } = request.params;
        const success = setIncomingCallScript(scriptId);
        
        if (success) {
            const newScript = getCurrentIncomingScript();
            reply.send({
                success: true,
                message: `Incoming call script changed to: ${newScript.name}`,
                script: {
                    id: newScript.id,
                    name: newScript.name,
                    description: newScript.description
                }
            });
        } else {
            reply.status(400).send({
                success: false,
                error: `Unknown script ID: ${scriptId}`
            });
        }
    } catch (error) {
        console.error('Error setting script:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Create custom incoming call script
fastify.post('/api/incoming-scripts/create', async (request, reply) => {
    try {
        const { id, name, description, campaignScript, voice, greetingAudio } = request.body;

        if (!id || !name || !campaignScript) {
            return reply.status(400).send({
                success: false,
                error: 'Missing required fields: id, name, campaignScript'
            });
        }

        const success = createCustomIncomingScript({
            id,
            name,
            description,
            campaignScript,
            voice,
            greetingAudio
        });
        
        if (success) {
            reply.send({
                success: true,
                message: `Custom script '${name}' created successfully`,
                scriptId: id
            });
        } else {
            reply.status(400).send({
                success: false,
                error: 'Failed to create custom script'
            });
        }
    } catch (error) {
        console.error('Error creating custom script:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Get script analytics
fastify.get('/api/incoming-scripts/analytics', async (request, reply) => {
    try {
        const analytics = getScriptMetrics();
        const totalCalls = Object.values(analytics).reduce((sum, metric) => sum + (metric.callCount || 0), 0);
        const totalHandleTime = Object.values(analytics).reduce((sum, metric) => {
            return sum + ((metric.totalHandleTime || 0));
        }, 0);
        const completedCalls = Object.values(analytics).reduce((sum, metric) => sum + (metric.completedCalls || 0), 0);
        const avgHandleTime = completedCalls > 0 ? Math.round(totalHandleTime / completedCalls) : 0;
        
        // Count active calls
        let activeCalls = 0;
        for (const [callSid, connectionData] of activeConnections) {
            if (connectionData.isIncomingCall) {
                activeCalls++;
            }
        }
        
        return {
            totalCalls: totalCalls,
            avgHandleTime: avgHandleTime,
            activeCalls: activeCalls,
            scriptMetrics: analytics
        };
    } catch (error) {
        reply.code(500).send({ error: 'Failed to get analytics', details: error.message });
    }
});

// Get call history
fastify.get('/api/incoming-scripts/history', async (request, reply) => {
    try {
        const limit = parseInt(request.query.limit) || 50;
        const history = getCallHistory(limit);
        return history;
    } catch (error) {
        reply.code(500).send({ error: 'Failed to get call history', details: error.message });
    }
});

// === NEW INCOMING SYSTEM ENDPOINTS (SEPARATE FROM OUTBOUND) ===

// Get all available incoming scenarios (NEW SYSTEM)
fastify.get('/api/incoming-scenarios', async (request, reply) => {
    try {
        const scenarios = listIncomingScenarios();
        const currentScenario = getCurrentIncomingScenario();
        reply.send({
            success: true,
            scenarios: scenarios,
            currentScenario: {
                id: currentScenario.id,
                name: currentScenario.name,
                description: currentScenario.description,
                agent: currentScenario.agent.name,
                voice: currentScenario.agent.voice,
                model: currentScenario.agent.model
            }
        });
    } catch (error) {
        console.error('Error listing incoming scenarios:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Select active incoming scenario (NEW SYSTEM)
fastify.post('/api/incoming-scenarios/select', async (request, reply) => {
    try {
        const { scenarioId } = request.body;
        const success = setActiveIncomingScenario(scenarioId);
        
        if (success) {
            const newScenario = getCurrentIncomingScenario();
            reply.send({
                success: true,
                message: `Incoming scenario activated: ${newScenario.name}`,
                scenario: {
                    id: newScenario.id,
                    name: newScenario.name,
                    description: newScenario.description,
                    agent: newScenario.agent.name,
                    voice: newScenario.agent.voice,
                    model: newScenario.agent.model
                }
            });
        } else {
            reply.status(400).send({
                success: false,
                error: `Unknown scenario ID: ${scenarioId}`
            });
        }
    } catch (error) {
        console.error('Error selecting scenario:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Get incoming analytics (NEW SYSTEM)
fastify.get('/api/incoming-analytics', async (request, reply) => {
    try {
        const analytics = getIncomingAnalytics();
        reply.send(analytics);
    } catch (error) {
        console.error('Error getting incoming analytics:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Get incoming call history (NEW SYSTEM)
fastify.get('/api/incoming-history', async (request, reply) => {
    try {
        const limit = parseInt(request.query.limit) || 50;
        const history = getIncomingCallHistory(limit);
        reply.send(history);
    } catch (error) {
        console.error('Error getting incoming history:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Test incoming call endpoint (NEW SYSTEM)
fastify.post('/api/incoming-test-call', async (request, reply) => {
    try {
        const currentScenario = getCurrentIncomingScenario();
        console.log(`🧪 Test call initiated with scenario: ${currentScenario.name}`);
        reply.send({
            success: true,
            message: `Test call configured with scenario: ${currentScenario.name}`,
            scenario: currentScenario.name
        });
    } catch (error) {
        console.error('Error testing incoming call:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Preview what scenario will be used for next incoming call
fastify.get('/api/incoming-preview', async (request, reply) => {
    try {
        const currentScenario = getCurrentIncomingScenario();
        reply.send({
            success: true,
            message: "Next incoming call will use this scenario:",
            activeScenario: {
                id: currentScenario.id,
                name: currentScenario.name,
                description: currentScenario.description,
                agent: {
                    name: currentScenario.agent.name,
                    personality: currentScenario.agent.personality,
                    voice: currentScenario.agent.voice,
                    model: currentScenario.agent.model
                },
                category: currentScenario.category,
                greeting: currentScenario.conversationFlow?.opening?.greeting || "No greeting configured"
            }
        });
    } catch (error) {
        console.error('Error getting incoming preview:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Store for manually configured incoming scenario
let manualIncomingConfig = null;

// Configure incoming scenario with manual script (for frontend manual selection)
fastify.post('/api/configure-incoming-scenario', async (request, reply) => {
    try {
        console.log('🔍 [DEBUG] configure-incoming-scenario request body:', JSON.stringify(request.body).substring(0, 500));
        const { name, language, fromNumber, voice, model, country, script, isActive } = request.body;

        if (!script || !name) {
            console.log('❌ [DEBUG] Missing required fields - script:', !!script, 'name:', !!name);
            console.log('❌ [DEBUG] Script type:', typeof script, 'Name type:', typeof name);
            return reply.status(400).send({
                success: false,
                error: 'Missing required fields: script and name'
            });
        }

        if (isActive) {
            // According to CAMPAIGN_SCRIPT_POLICY.md: NO SYSTEM PROMPTS
            // Handle both JSON objects and formatted text scripts from frontend
            let scriptObject;
            try {
                if (typeof script === 'string') {
                    // Try to parse as JSON first
                    try {
                        scriptObject = JSON.parse(script);
                        console.log('📋 [DEBUG] Parsed script as JSON object');
                    } catch (jsonError) {
                        // If not JSON, treat as formatted text script (legacy format)
                        console.log('📋 [DEBUG] Script is formatted text, creating minimal campaign object');
                        scriptObject = {
                            agentPersona: {
                                name: name || 'AI Assistant',
                                tone: 'Professional and helpful'
                            },
                            script: {
                                instructions: script
                            },
                            callContext: {
                                purpose: 'Incoming call handling'
                            }
                        };
                    }
                } else {
                    scriptObject = script;
                    console.log('📋 [DEBUG] Script is already an object');
                }
            } catch (error) {
                console.error('❌ [DEBUG] Error processing script:', error);
                return reply.status(400).send({
                    success: false,
                    error: 'Error processing campaign script'
                });
            }

            // Add language-specific behavioral guidelines to the campaign script
            if (language && scriptObject.agentPersona) {
                if (!scriptObject.agentPersona.behavioralGuidelines) {
                    scriptObject.agentPersona.behavioralGuidelines = {};
                }

                if (language === 'es') {
                    scriptObject.agentPersona.behavioralGuidelines.languageRequirement = 'HABLA SOLO EN ESPAÑOL! Never speak English. You MUST respond only in Spanish language.';
                    scriptObject.language = 'es';
                } else if (language === 'cz') {
                    scriptObject.agentPersona.behavioralGuidelines.languageRequirement = 'MLUVTE POUZE ČESKY! NEVER speak English. You MUST respond only in Czech language. Česky mluvte vždy!';
                    scriptObject.language = 'cz';
                }
            }

            // Add call context if not present
            if (!scriptObject.callContext) {
                scriptObject.callContext = {
                    purpose: `Handle incoming call to ${fromNumber || '+18455954168'}`,
                    callDirection: 'customer_calls_agent',
                    dateTimeContext: `Current date and time: ${new Date().toLocaleString('en-US', { timeZone: 'America/New_York' })} (Eastern Time)`
                };
            }
            // Store the manual configuration with campaign script
            manualIncomingConfig = {
                name: name,
                script: null, // No longer using system prompts
                originalScript: scriptObject, // Store the campaign script object
                voice: voice || 'Kore',
                model: model || 'gemini-2.5-flash-preview-native-audio-dialog',
                language: language || 'en',
                fromNumber: fromNumber,
                country: country,
                isActive: true,
                timestamp: new Date().toISOString()
            };

            console.log(`📞 MANUAL CONFIG: Activated manual incoming scenario: ${name}`);
            console.log(`🎤 MANUAL CONFIG: Voice: ${manualIncomingConfig.voice}, Model: ${manualIncomingConfig.model}`);
            console.log(`🌍 MANUAL CONFIG: Language: ${manualIncomingConfig.language}`);
            console.log(`📋 MANUAL CONFIG: Campaign script configured (${JSON.stringify(scriptObject).length} chars)`);
            console.log(`✅ MANUAL CONFIG: Using campaign script format (no system prompts)`);

            reply.send({
                success: true,
                message: `Manual incoming scenario activated: ${name} (${(language || 'en').toUpperCase()})`,
                config: {
                    name: manualIncomingConfig.name,
                    voice: manualIncomingConfig.voice,
                    model: manualIncomingConfig.model,
                    language: manualIncomingConfig.language
                }
            });
        } else {
            // Deactivate manual configuration
            manualIncomingConfig = null;
            console.log(`📞 MANUAL CONFIG: Deactivated manual incoming scenario`);

            reply.send({
                success: true,
                message: 'Manual incoming scenario deactivated',
                config: null
            });
        }
    } catch (error) {
        console.error('Error configuring manual incoming scenario:', error);
        reply.status(500).send({ success: false, error: error.message });
    }
});

// Incoming call handler (Twilio Webhook)
fastify.all('/incoming-call', async (request, reply) => {
    console.log('📞 Incoming call received');
    
    const twiml = new twilio.twiml.VoiceResponse();

    // Play audio file if URL is provided in environment variables
    if (GREETING_AUDIO_URL && GREETING_AUDIO_URL !== 'https://your-domain.com/greeting.mp3') {
        console.log(`🎵 Playing greeting audio from: ${GREETING_AUDIO_URL}`);
        twiml.play({}, GREETING_AUDIO_URL);
    } else {
        console.log("⏭️ GREETING_AUDIO_URL not set or is placeholder, skipping initial Play.");
    }

    // Connect the media stream
    const connect = twiml.connect();
    const publicUrl = new URL(PUBLIC_URL);
    const wsProtocol = publicUrl.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = `${wsProtocol}://${publicUrl.host}/media-stream`;
    connect.stream({ url: wsUrl });
    console.log(`🔗 Generated TwiML with Stream URL: ${wsUrl}`);

    reply.header('Content-Type', 'application/xml');
    return twiml.toString();
});

// WebSocket handler for media streams (Gemini Live API integration)
fastify.register(async (fastify) => {
    fastify.get('/media-stream', { websocket: true }, (connection, req) => {
        let tempCallSid = "pending";
        console.log(`🔌 [${tempCallSid}] Client connected to /media-stream`);

        // Skip JWT validation for Twilio media streams (they use different auth)
        // Twilio streams are authenticated via webhook signatures
        let streamSid = null;
        let callSid = null;
        let geminiSession = null;
        let isSessionActive = false;

        // Check if this is an incoming call (no config stored) or outbound call (has config)
        const isIncomingCall = !nextCallConfig.aiInstructions;

        let sessionAIInstructions, sessionVoice, sessionModel, sessionTargetName, sessionTargetPhoneNumber;
        
        if (isIncomingCall) {
            // This is an incoming call - check for manual configuration first
            if (manualIncomingConfig && manualIncomingConfig.isActive) {
                // Use manually configured script from frontend
                // According to CAMPAIGN_SCRIPT_POLICY.md: Send FULL campaign script as system instructions
                try {
                    if (typeof manualIncomingConfig.originalScript === 'object') {
                        sessionAIInstructions = `CAMPAIGN SCRIPT - Follow this script exactly:\n\n${JSON.stringify(manualIncomingConfig.originalScript, null, 2)}`;
                        console.log('📋 [INCOMING-MANUAL] Using full campaign script as system instructions (length:', sessionAIInstructions.length, 'chars)');
                    } else {
                        // Fallback: treat as legacy system prompt (temporary compatibility)
                        sessionAIInstructions = manualIncomingConfig.script;
                    }
                } catch (error) {
                    console.error(`❌ [${tempCallSid}] Error extracting AI instructions from manual config:`, error);
                    sessionAIInstructions = manualIncomingConfig.script; // Fallback
                }

                sessionVoice = getValidGeminiVoice(manualIncomingConfig.voice);
                sessionModel = getValidGeminiModel(manualIncomingConfig.model || DEFAULT_GEMINI_MODEL);
                sessionTargetName = "Incoming Caller";
                sessionTargetPhoneNumber = "Unknown";

                console.log(`📞 [${tempCallSid}] INCOMING CALL - Using MANUAL CONFIG: ${manualIncomingConfig.name}`);
                console.log(`🎤 [${tempCallSid}] Manual Voice: ${sessionVoice}, Model: ${sessionModel}`);
                console.log(`📋 [${tempCallSid}] Manual AI Instructions: ${sessionAIInstructions.substring(0, 100)}...`);
            } else {
                // Convert predefined scenario to campaign script format and extract AI instructions
                const incomingScenario = getCurrentIncomingScenario();
                const campaignScript = convertIncomingScenarioToCampaignScript(incomingScenario);

                try {
                    // According to CAMPAIGN_SCRIPT_POLICY.md: Send FULL campaign script as system instructions
                    sessionAIInstructions = `CAMPAIGN SCRIPT - Follow this script exactly:\n\n${JSON.stringify(campaignScript, null, 2)}`;
                    console.log('📋 [INCOMING-SCENARIO] Using full campaign script as system instructions (length:', sessionAIInstructions.length, 'chars)');
                } catch (error) {
                    console.error(`❌ [${tempCallSid}] Error converting incoming scenario to campaign script:`, error);
                    // Fallback: no hardcoded instructions
                    sessionAIInstructions = ''; // Campaign script should provide all instructions
                }

                sessionVoice = getValidGeminiVoice(incomingScenario.agent.voice);
                sessionModel = getValidGeminiModel(incomingScenario.agent.model || DEFAULT_GEMINI_MODEL);
                sessionTargetName = "Incoming Caller";
                sessionTargetPhoneNumber = "Unknown";

                // Record call start for NEW incoming system metrics
                recordIncomingCallStart(incomingScenario.id, tempCallSid);

                console.log(`📞 [${tempCallSid}] INCOMING CALL - Using converted scenario: ${incomingScenario.name}`);
                console.log(`🎭 [${tempCallSid}] Agent: ${incomingScenario.agent.name} (${incomingScenario.agent.tone})`);
                console.log(`📋 [${tempCallSid}] Category: ${incomingScenario.category}`);
                console.log(`🎤 [${tempCallSid}] Voice: ${sessionVoice}, Model: ${sessionModel}`);
                console.log(`✅ [${tempCallSid}] Using campaign script format (converted from legacy)`);
            }
        } else {
            // This is an outbound call - use the stored config
            sessionAIInstructions = nextCallConfig.aiInstructions;
            sessionVoice = getValidGeminiVoice(nextCallConfig.voice);
            sessionModel = getValidGeminiModel(nextCallConfig.model);
            sessionTargetName = nextCallConfig.targetName;
            sessionTargetPhoneNumber = nextCallConfig.targetPhoneNumber;
            console.log(`📞 [${tempCallSid}] OUTBOUND CALL - Using config for: ${sessionTargetName}`);
        }

        // Reset global config immediately for the *next* call
        nextCallConfig = {
            aiInstructions: null, // Will be populated from campaign script
            voice: DEFAULT_VOICE,
            model: DEFAULT_GEMINI_MODEL,
            targetName: null,
            targetPhoneNumber: null
        };
        
        console.log(`🔧 [${tempCallSid}] Applied config for this call (Target: ${sessionTargetName}, Model: ${sessionModel}). Global config reset for next call.`);
        console.log(`🤖 [${tempCallSid}] USING MODEL: ${sessionModel} (${AVAILABLE_GEMINI_MODELS[sessionModel]?.name || 'Unknown'})`);
        console.log(`🎤 [${tempCallSid}] USING VOICE: ${sessionVoice}`);
        console.log(`🎭 [${tempCallSid}] CALL TYPE: ${isIncomingCall ? 'INCOMING' : 'OUTBOUND'}`);

        // Enhanced Gemini Live session initialization with retry logic
        async function initializeGeminiSession(retryCount = 0) {
            const maxRetries = 3;
            const retryDelay = 1000 * (retryCount + 1); // Exponential backoff

            try {
                console.log(`🤖 [${callSid || tempCallSid}] Initializing Gemini Live session (attempt ${retryCount + 1}/${maxRetries + 1}) with model: ${sessionModel}, voice: ${sessionVoice}`);

                // Track session start time for debugging
                const sessionStartTime = Date.now();

                // Create Gemini session with enhanced configuration
                const newGeminiSession = await geminiClient.live.connect({
                    model: sessionModel,
                    callbacks: {
                        onopen: () => {
                            console.log(`✅ [${callSid || tempCallSid}] Gemini session opened successfully`);
                            console.log(`🔍 [${callSid || tempCallSid}] Session opened at: ${new Date().toISOString()}`);
                            isSessionActive = true;

                            // Initialize Deepgram transcription for conversation logging
                            if (callSid) {
                                const connData = activeConnections.get(callSid);
                                if (connData) {
                                    TranscriptionManager.initializeTranscription(callSid, connData).then(dgConnection => {
                                        if (dgConnection && connData) {
                                            connData.deepgramConnection = dgConnection;
                                        }
                                    }).catch(error => {
                                        console.warn(`⚠️ [${callSid}] Failed to initialize Deepgram:`, error);
                                    });
                                }
                            }

                            // Reset retry count on successful connection
                            retryCount = 0;

                            // Track successful connection
                            if (callSid) {
                                healthMonitor.trackConnection(callSid, 'connected', {
                                    model: sessionModel,
                                    voice: sessionVoice
                                });

                                // Save initial session context
                                contextManager.saveSessionContext(callSid, {
                                    aiInstructions: sessionAIInstructions,
                                    voice: sessionVoice,
                                    model: sessionModel,
                                    targetName: sessionTargetName,
                                    targetPhoneNumber: sessionTargetPhoneNumber,
                                    isIncomingCall: isIncomingCall,
                                    isSessionActive: true,
                                    summaryRequested: false,
                                    streamSid: streamSid
                                });
                            }
                        },

                        onerror: (error) => {
                            console.error(`❌ [${callSid || tempCallSid}] Gemini session error:`, error);
                            console.error(`❌ [${callSid || tempCallSid}] Error details:`, JSON.stringify(error, null, 2));
                            console.error(`❌ [${callSid || tempCallSid}] Error stack:`, error?.stack);
                            isSessionActive = false;

                            // Track connection failure
                            if (callSid) {
                                healthMonitor.trackConnection(callSid, 'failed', {
                                    error: error.message,
                                    retryCount
                                });

                                // Save context for potential recovery
                                contextManager.saveSessionContext(callSid, {
                                    aiInstructions: sessionAIInstructions,
                                    voice: sessionVoice,
                                    model: sessionModel,
                                    targetName: sessionTargetName,
                                    targetPhoneNumber: sessionTargetPhoneNumber,
                                    isIncomingCall: isIncomingCall,
                                    isSessionActive: false,
                                    summaryRequested: false,
                                    streamSid: streamSid
                                });
                            }

                            // Simple retry logic - NO RECOVERY TRIGGERS
                            if (retryCount < maxRetries) {
                                console.log(`🔄 [${callSid || tempCallSid}] Attempting to reconnect in ${retryDelay}ms...`);
                                setTimeout(() => {
                                    initializeGeminiSession(retryCount + 1);
                                }, retryDelay);
                            } else {
                                console.error(`💥 [${callSid || tempCallSid}] Max retries reached - session failed`);
                                // NO RECOVERY - Let natural session handling take over
                            }
                        },

                        onclose: () => {
                            console.log(`🔌 [${callSid || tempCallSid}] Gemini session closed`);
                            isSessionActive = false;

                            // ONLY TRIGGER RECOVERY ON ACTUAL SESSION CLOSE (not errors)
                            if (callSid && contextManager.canRecover(callSid)) {
                                console.log(`🚑 [${callSid}] Gemini session dropped - triggering context recovery`);
                                setTimeout(() => {
                                    recoveryManager.recoverSession(callSid, 'gemini_session_dropped');
                                }, 1000);
                            }
                        },

                        onmessage: async (message) => {
                            try {
                                // Log Gemini API messages for debugging (excluding audio packets)
                                const hasAudio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                                if (!hasAudio) {
                                    console.log(`📨 [${callSid || tempCallSid}] Gemini API message (non-audio):`, JSON.stringify(message, null, 2));
                                } else {
                                    console.log(`🎵 [${callSid || tempCallSid}] Gemini audio packet received (${message.serverContent?.modelTurn?.parts?.[0]?.inlineData?.data?.length || 0} bytes)`);
                                }

                                const connectionData = activeConnections.get(callSid);
                                if (!connectionData) {
                                    console.warn(`⚠️ [${callSid || tempCallSid}] No connection data found for message handling`);
                                    return;
                                }

                                // Enhanced message validation and handling
                                if (!message || !message.serverContent) {
                                    console.warn(`⚠️ [${callSid || tempCallSid}] Received invalid message structure`);
                                    return;
                                }

                                // Handle audio response from Gemini with validation
                                const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                                if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                    // Validate audio data before processing
                                    if (!audio.data || audio.data.length === 0) {
                                        console.warn(`⚠️ [${callSid || tempCallSid}] Received empty audio data from Gemini`);
                                        return;
                                    }

                                    // Convert Gemini PCM audio to Twilio μ-law format and send back
                                    if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
                                        try {
                                            // Monitor Gemini output quality before conversion
                                            const geminiPcmBuffer = Buffer.from(audio.data, 'base64');

                                            // Validate buffer size
                                            if (geminiPcmBuffer.length === 0 || geminiPcmBuffer.length % 2 !== 0) {
                                                console.warn(`⚠️ [${callSid || tempCallSid}] Invalid audio buffer size: ${geminiPcmBuffer.length}`);
                                                return;
                                            }

                                            const geminiFloat32 = new Float32Array(geminiPcmBuffer.length / 2);
                                            for (let i = 0; i < geminiFloat32.length; i++) {
                                                geminiFloat32[i] = geminiPcmBuffer.readInt16LE(i * 2) / 32768.0;
                                            }

                                            // Analyze Gemini output quality with validation
                                            const qualityMetrics = AudioProcessor.audioQualityMonitor.analyze(geminiFloat32, 'gemini-output');

                                            // Skip processing if audio quality is too poor - RELAXED FOR INCOMING CALLS
                                            if (qualityMetrics && qualityMetrics.silencePercentage > 98) {
                                                console.warn(`⚠️ [${callSid || tempCallSid}] Skipping extremely silent audio (${qualityMetrics.silencePercentage.toFixed(1)}% silence)`);
                                                return;
                                            }

                                            // Save debug audio if enabled
                                            await AudioProcessor.saveAudioDebug(geminiFloat32, 'gemini-output', 24000);

                                            // Convert Gemini's PCM audio to μ-law for Twilio with enhanced processing
                                            const convertedAudio = AudioProcessor.convertPCMToUlaw(audio.data);

                                            const audioDelta = {
                                                event: 'media',
                                                streamSid: streamSid,
                                                media: {
                                                    payload: convertedAudio
                                                }
                                            };
                                            connectionData.twilioWs.send(JSON.stringify(audioDelta));

                                            // Enhanced logging with quality metrics
                                            const qualitySummary = AudioProcessor.audioQualityMonitor.getSummary();
                                            console.log(`🔊 [${callSid}] Sent enhanced audio to Twilio (${convertedAudio.length} chars) - Peak: ${qualitySummary.peakLevel}, Avg: ${qualitySummary.averageLevel}`);
                                        } catch (audioConversionError) {
                                            console.error(`❌ [${callSid}] Error converting Gemini audio for Twilio:`, audioConversionError);
                                            // Fallback: send original audio
                                            const audioDelta = {
                                                event: 'media',
                                                streamSid: streamSid,
                                                media: {
                                                    payload: audio.data
                                                }
                                            };
                                            connectionData.twilioWs.send(JSON.stringify(audioDelta));
                                        }
                                    }
                                }

                                // Handle text response for summary collection and conversation logging
                                const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                                if (text) {
                                    console.log(`💬 [${callSid}] Gemini response: ${text.substring(0, 100)}...`);

                                    // Update AI responsiveness tracking
                                    connectionData.lastAIResponse = Date.now();
                                    connectionData.responseTimeouts = 0; // Reset timeout counter
                                    connectionData.connectionQuality = 'good';

                                    // Log conversation for recovery purposes
                                    if (connectionData.conversationLog) {
                                        connectionData.conversationLog.push({
                                            role: 'assistant',
                                            content: text,
                                            timestamp: Date.now(),
                                            messageId: `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                                        });

                                        // Keep only last 30 messages for better context (increased from 20)
                                        if (connectionData.conversationLog.length > 30) {
                                            connectionData.conversationLog = connectionData.conversationLog.slice(-30);
                                        }
                                    }

                                    // Also add to full transcript for complete recovery
                                    if (connectionData.fullTranscript) {
                                        connectionData.fullTranscript.push({
                                            role: 'assistant',
                                            content: text,
                                            timestamp: Date.now(),
                                            confidence: 1.0 // AI responses have full confidence
                                        });

                                        // Keep last 50 messages for full context
                                        if (connectionData.fullTranscript.length > 50) {
                                            connectionData.fullTranscript = connectionData.fullTranscript.slice(-50);
                                        }
                                    }

                                    // Note: Summary generation now happens after call ends, not during the call
                                }

                            } catch (error) {
                                console.error(`❌ [${callSid}] Error processing Gemini message:`, error);
                            }
                        },

                        onclose: (event) => {
                            console.log(`🔒 [${callSid || tempCallSid}] Gemini session closed`);
                            console.log(`🔍 [${callSid || tempCallSid}] Close event details:`, event);
                            console.log(`🔍 [${callSid || tempCallSid}] Session was active for: ${Date.now() - sessionStartTime}ms`);
                            isSessionActive = false;
                            const connectionData = activeConnections.get(callSid);
                            if (callSid && connectionData) {
                                if (connectionData.summaryTimeoutId) {
                                    console.log(`⏰ [${callSid}] Clearing summary timeout due to Gemini session close`);
                                    clearTimeout(connectionData.summaryTimeoutId);
                                }
                                if (connectionData.summaryRequested && !connectionData.summaryReceived) {
                                    console.log(`📝 [${callSid}] Gemini session closed before summary fully received. Saving placeholder`);
                                    saveSummaryInfo(callSid, connectionData.summaryText || "Call ended before summary could be generated by AI.", "neutral", "incomplete", connectionData.targetName, connectionData.targetPhoneNumber);
                                }
                                if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
                                    console.log(`🔒 [${callSid}] Closing associated Twilio connection from Gemini close handler`);
                                    connectionData.twilioWs.close();
                                }

                                // Clean up Deepgram connection
                                if (connectionData.deepgramConnection) {
                                    TranscriptionManager.closeTranscription(callSid, connectionData.deepgramConnection);
                                }

                                activeConnections.delete(callSid);
                                console.log(`🗑️ [${callSid}] Connection data deleted from map due to Gemini session close`);
                            }
                        }
                    },
                    config: {
                        responseModalities: [Modality.AUDIO],
                        speechConfig: {
                            voiceConfig: {
                                prebuiltVoiceConfig: {
                                    voiceName: sessionVoice
                                }
                            }
                        }
                        // NO systemInstruction according to CAMPAIGN_SCRIPT_POLICY.md
                    },
                    // Move generation config to top level to avoid deprecation warning
                    temperature: 1.1,
                    topP: 0.95,
                    topK: 40,
                    maxOutputTokens: 8192,
                    responseMimeType: "text/plain"
                });

                // Assign to global variable for other parts of the code
                geminiSession = newGeminiSession;

                // Send AI instructions as user message according to CAMPAIGN_SCRIPT_POLICY.md
                if (sessionAIInstructions) {
                    console.log(`📝 [${callSid || tempCallSid}] Sending AI instructions to Gemini (${sessionAIInstructions.length} chars)`);
                    console.log(`📝 [${callSid || tempCallSid}] AI instructions preview: ${sessionAIInstructions.substring(0, 200)}...`);

                    const instructionsPayload = {
                        turns: [{
                            role: 'user',
                            parts: [{
                                text: sessionAIInstructions
                            }]
                        }],
                        turnComplete: true
                    };

                    console.log(`📤 [${callSid || tempCallSid}] AI instructions payload:`, JSON.stringify(instructionsPayload, null, 2));

                    try {
                        newGeminiSession.sendClientContent(instructionsPayload);
                        console.log(`✅ [${callSid || tempCallSid}] AI instructions sent successfully`);
                    } catch (sendError) {
                        console.error(`❌ [${callSid || tempCallSid}] Error sending AI instructions:`, sendError);
                    }
                }

                // Send appropriate prepare message based on call direction IMMEDIATELY - NO DELAY!
                const prepareMessage = isIncomingCall
                    ? (AI_PREPARE_MESSAGE_INCOMING || "IMPORTANT: This is an INCOMING call - THEY are calling YOU. Wait for the caller to speak first.")
                    : (AI_PREPARE_MESSAGE_OUTBOUND || AI_PREPARE_MESSAGE || "IMPORTANT: This is an OUTBOUND call - YOU are calling THEM. Speak first immediately.");

                if (prepareMessage) {
                    const callType = isIncomingCall ? "INCOMING" : "OUTBOUND";
                    console.log(`🎯 [${callSid || tempCallSid}] ${callType} CALL - Scheduling AI prepare message after session is ready`);

                    // Wait for session to be fully established before sending message
                    setTimeout(() => {
                        if (newGeminiSession && activeConnections.has(callSid || tempCallSid)) {
                            console.log(`🎯 [${callSid || tempCallSid}] Now sending ${callType} prepare message: ${prepareMessage}`);
                            newGeminiSession.sendClientContent({
                                turns: [{
                                    role: 'user',
                                    parts: [{
                                        text: prepareMessage
                                    }]
                                }],
                                turnComplete: true
                            });
                        } else {
                            console.log(`⚠️ [${callSid || tempCallSid}] Session no longer active, skipping prepare message`);
                        }
                    }, 1000); // 1-second delay to ensure session is ready
                }

                return newGeminiSession;

            } catch (error) {
                console.error(`❌ [${callSid || tempCallSid}] Error initializing Gemini session:`, error);
                return null;
            }
        }

        connection.on('message', (rawMessage) => {
            try {
                const message = rawMessage.toString();
                const data = JSON.parse(message);

                if (data.event === 'start') {
                    streamSid = data.start.streamSid;
                    callSid = data.start.callSid;
                    tempCallSid = callSid;
                    console.log(`🎬 [${callSid}] Incoming stream started: ${streamSid}`);
                    console.log(`🤖 [${callSid}] ACTIVE MODEL: ${sessionModel} | VOICE: ${sessionVoice} | TARGET: ${sessionTargetName || 'None'}`);

                    if (callSid) {
                        // Store connection data with enhanced context tracking
                        activeConnections.set(callSid, {
                            geminiSession: null,
                            twilioWs: connection,
                            summaryRequested: false,
                            summaryText: "",
                            summaryReceived: false,
                            summaryTimeoutId: null,
                            targetName: sessionTargetName,
                            targetPhoneNumber: sessionTargetPhoneNumber,
                            isIncomingCall: isIncomingCall,
                            scriptId: isIncomingCall ? getCurrentIncomingScenario().id : null,
                            conversationLog: [], // Track conversation for recovery
                            fullTranscript: [], // Complete conversation transcript with timestamps
                            lastContextSave: Date.now(),
                            contextSaveInterval: null, // For periodic context saving
                            lastAIResponse: Date.now(), // Track AI responsiveness
                            responseTimeouts: 0, // Count consecutive timeouts
                            connectionQuality: 'good', // Track connection quality
                            originalAIInstructions: sessionAIInstructions, // Store original instructions for recovery
                            deepgramConnection: null // For speech-to-text transcription
                        });
                        console.log(`💾 [${callSid}] Stored WebSocket connection data in map (Target: ${sessionTargetName})`);

                        // Initialize Gemini session
                        initializeGeminiSession().then(session => {
                            if (session) {
                                const connectionData = activeConnections.get(callSid);
                                if (connectionData) {
                                    connectionData.geminiSession = session;
                                    geminiSession = session;

                                    // Start periodic context saving and AI responsiveness monitoring
                                    connectionData.contextSaveInterval = setInterval(() => {
                                        if (connectionData && isSessionActive) {
                                            // Save context
                                            contextManager.saveSessionContext(callSid, {
                                                aiInstructions: sessionAIInstructions,
                                                voice: sessionVoice,
                                                model: sessionModel,
                                                targetName: sessionTargetName,
                                                targetPhoneNumber: sessionTargetPhoneNumber,
                                                isIncomingCall: isIncomingCall,
                                                isSessionActive: isSessionActive,
                                                summaryRequested: connectionData.summaryRequested,
                                                summaryText: connectionData.summaryText,
                                                streamSid: streamSid,
                                                conversationLog: connectionData.conversationLog,
                                                fullTranscript: connectionData.fullTranscript
                                            });
                                            connectionData.lastContextSave = Date.now();

                                            // Check AI responsiveness (if no response in 15 seconds during active conversation)
                                            const timeSinceLastResponse = Date.now() - connectionData.lastAIResponse;
                                            if (timeSinceLastResponse > 15000) {
                                                connectionData.responseTimeouts++;
                                                connectionData.connectionQuality = connectionData.responseTimeouts > 2 ? 'poor' : 'degraded';

                                                console.warn(`⚠️ [${callSid}] AI responsiveness issue detected. Time since last response: ${timeSinceLastResponse}ms, Timeouts: ${connectionData.responseTimeouts}`);

                                                // Log AI unresponsiveness but DON'T trigger recovery
                                                if (connectionData.responseTimeouts >= 3) {
                                                    console.log(`⚠️ [${callSid}] AI unresponsive (${connectionData.responseTimeouts} timeouts) - but continuing session`);
                                                }
                                            }
                                        }
                                    }, 10000); // Save context and check responsiveness every 10 seconds
                                }
                            }
                        });
                    }
                } else if (data.event === 'media') {
                    if (geminiSession && isSessionActive) {
                        try {
                            // Convert Twilio μ-law audio to PCM format suitable for Gemini
                            // Twilio sends base64 encoded μ-law audio
                            const ulawBuffer = Buffer.from(data.media.payload, 'base64');

                            // Convert μ-law to PCM with enhanced quality
                            const pcmBuffer = AudioProcessor.convertUlawToPCM(ulawBuffer);

                            // Convert PCM to Float32Array for Gemini with quality monitoring
                            const float32Audio = AudioProcessor.pcmToFloat32Array(pcmBuffer);

                            // Monitor input audio quality
                            AudioProcessor.audioQualityMonitor.analyze(float32Audio, 'twilio-input');

                            // Save debug audio if enabled
                            AudioProcessor.saveAudioDebug(float32Audio, 'twilio-input', 8000).catch(err =>
                                console.warn(`⚠️ [${callSid}] Debug audio save failed:`, err)
                            );

                            // Create Gemini audio blob
                            const geminiAudioBlob = AudioProcessor.createGeminiAudioBlob(float32Audio);

                            // Send audio to Deepgram for transcription (safe check)
                            if (connectionData && connectionData.deepgramConnection) {
                                try {
                                    connectionData.deepgramConnection.send(ulawBuffer);
                                } catch (dgError) {
                                    console.warn(`⚠️ [${callSid}] Deepgram send error:`, dgError);
                                }
                            }

                            // Send properly formatted audio to Gemini Live API using new API
                            geminiSession.sendRealtimeInput({
                                media: {
                                    mimeType: geminiAudioBlob.mimeType,
                                    data: geminiAudioBlob.data
                                }
                            });

                            // Log audio processing (less verbose in production)
                            if (Math.random() < 0.01) { // Log 1% of audio packets to avoid spam
                                console.log(`🎤 [${callSid}] Processed audio: ${ulawBuffer.length}B μ-law → ${pcmBuffer.length}B PCM → ${float32Audio.length} samples`);
                            }
                        } catch (audioError) {
                            console.error(`❌ [${callSid}] Error processing audio for Gemini:`, audioError);
                        }
                    }
                } else if (data.event === 'stop') {
                    console.log(`🛑 [${callSid}] Twilio media stream stopped event received`);
                    // Note: 'stop' event doesn't mean the call ended, just the stream for this TwiML verb.
                    // Summary logic is handled by call-status or connection close.
                }
            } catch (error) {
                console.error(`❌ [${callSid || tempCallSid}] Error handling Twilio message:`, error);
            }
        });

        // Enhanced connection close handling with cleanup and error analysis
        connection.on('close', (code, reason) => {
            const reasonStr = reason ? reason.toString() : 'No reason provided';
            console.log(`🔌 [${callSid || tempCallSid}] Twilio connection closed. Code: ${code}, Reason: ${reasonStr}`);

            // Analyze close code for better debugging
            const closeAnalysis = analyzeCloseCode(code);
            if (closeAnalysis.isError) {
                console.error(`❌ [${callSid || tempCallSid}] Connection closed with error: ${closeAnalysis.description}`);

                // Track connection failure and attempt recovery if appropriate
                if (callSid) {
                    healthMonitor.trackConnection(callSid, 'disconnected', {
                        code,
                        reason: reasonStr,
                        isError: true
                    });

                    // Log connection close but DON'T trigger recovery on Twilio disconnections
                    if (code !== 1000) {
                        console.log(`⚠️ [${callSid}] Unexpected Twilio disconnection (code: ${code}) - but NOT triggering recovery`);
                    }
                }
            } else {
                console.log(`✅ [${callSid || tempCallSid}] Connection closed normally: ${closeAnalysis.description}`);

                // Track normal disconnection
                if (callSid) {
                    healthMonitor.trackConnection(callSid, 'disconnected', {
                        code,
                        reason: reasonStr,
                        isError: false
                    });
                }
            }

            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                // Enhanced cleanup with audio quality summary
                const audioSummary = AudioProcessor.audioQualityMonitor.getSummary();
                console.log(`🎵 [${callSid}] Final audio quality summary:`, audioSummary);

                // Clean up any pending timers or resources
                if (connectionData.heartbeatInterval) {
                    clearInterval(connectionData.heartbeatInterval);
                }
                if (connectionData.contextSaveInterval) {
                    clearInterval(connectionData.contextSaveInterval);
                }

                // If summary hasn't been requested/received yet, and Gemini session is open, request it now
                if (!connectionData.summaryRequested && !connectionData.summaryReceived && connectionData.geminiSession && isSessionActive) {
                    console.log(`📝 [${callSid}] Twilio connection closed before summary completed. Requesting summary now`);
                    requestSummary(callSid, connectionData);
                } else if (!connectionData.geminiSession || !isSessionActive) {
                    console.log(`🗑️ [${callSid}] Twilio connection closed, Gemini session already closed or summary handled. Cleaning up map entry`);
                    if (connectionData.summaryTimeoutId) clearTimeout(connectionData.summaryTimeoutId);
                    activeConnections.delete(callSid);
                } else {
                    console.log(`⏳ [${callSid}] Twilio connection closed, summary already requested/received. Letting other handlers manage cleanup`);
                }
            } else {
                console.log(`❓ [${callSid || tempCallSid}] Twilio connection closed, but no active connection data found. Already cleaned up?`);
            }
        });

        // Enhanced error handling for WebSocket connections
        connection.on('error', (error) => {
            console.error(`❌ [${callSid || tempCallSid}] WebSocket error:`, error);

            // Log additional error details for debugging
            if (error.code) {
                console.error(`❌ [${callSid || tempCallSid}] Error code: ${error.code}`);
            }
            if (error.message) {
                console.error(`❌ [${callSid || tempCallSid}] Error message: ${error.message}`);
            }

            // Attempt graceful cleanup
            const connectionData = activeConnections.get(callSid);
            if (connectionData) {
                // Clear heartbeat if exists
                if (connectionData.heartbeatInterval) {
                    clearInterval(connectionData.heartbeatInterval);
                }
                // Clear context save interval if exists
                if (connectionData.contextSaveInterval) {
                    clearInterval(connectionData.contextSaveInterval);
                }

                // Close Gemini session if open
                if (connectionData.geminiSession) {
                    try {
                        connectionData.geminiSession.close();
                    } catch (closeError) {
                        console.error(`❌ [${callSid || tempCallSid}] Error closing Gemini session:`, closeError);
                    }
                }
            }
        });

        // Add connection heartbeat for better connection monitoring
        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            connectionData.heartbeatInterval = setInterval(() => {
                if (connection.readyState === WebSocket.OPEN) {
                    // Send a ping to keep connection alive
                    try {
                        connection.ping();
                    } catch (pingError) {
                        console.warn(`⚠️ [${callSid}] Failed to send ping:`, pingError);
                    }
                } else {
                    console.warn(`⚠️ [${callSid}] Connection not open, clearing heartbeat`);
                    clearInterval(connectionData.heartbeatInterval);
                }
            }, 30000); // Ping every 30 seconds
        }
    });
});

// WebSocket handler for local audio testing (direct browser mic/speaker)
fastify.register(async (fastify) => {
    fastify.get('/local-audio-session', { websocket: true }, (connection, req) => {
        console.log('🎤 Local audio session connected');
        console.log('🔍 [DEBUG] Connection state:', connection.readyState);
        console.log('🔍 [DEBUG] Request URL:', req.url);
        console.log('🔍 [DEBUG] Request headers:', req.headers);

        // WebSocket authentication is handled by the main preHandler middleware
        // For local audio sessions, authentication is already validated
        console.log('✅ Local audio session connected (authentication handled by middleware)');

        // Send session ready confirmation immediately when WebSocket connects
        if (connection.readyState === 1) {
            connection.send(JSON.stringify({
                type: 'session_ready',
                message: 'WebSocket established, ready for configuration'
            }));
            console.log('📤 Session ready confirmation sent to frontend immediately');
        }

        let geminiSession = null;
        let isSessionActive = false;
        let sessionConfig = null;
        let conversationHistory = []; // Store conversation for context preservation
        let keepAliveInterval = null; // Heartbeat to keep session alive
        let lastActivityTime = Date.now(); // Track last user activity
        let deepgramConnection = null; // For speech-to-text transcription
        let speechTranscript = []; // Store speech transcriptions for better recovery context

        // Initialize Gemini session for local testing
        const initializeLocalGeminiSession = async (config) => {
            try {
                console.log('🚀 Initializing local Gemini session with config:', {
                    voice: config.voice,
                    model: config.model,
                    language: config.language
                });

                // Store session reference for callbacks
                let sessionInstance = null;

                console.log('🔄 Attempting to connect to Gemini with model:', config.model || DEFAULT_GEMINI_MODEL);

                // Parse task content first to get scriptContent
                let scriptObject = config.task;
                let scriptContent = config.task;

                // If the task is a string, try to parse it as JSON
                if (typeof config.task === 'string') {
                    try {
                        const parsed = JSON.parse(config.task);
                        if (parsed && typeof parsed === 'object') {
                            // The entire parsed object is the script object
                            scriptObject = parsed;
                            scriptContent = parsed;
                        }
                    } catch (e) {
                        // Keep original task if not JSON
                        console.log('🔍 Task is not JSON, treating as plain text');
                    }
                } else if (config.task && typeof config.task === 'object') {
                    // Task is already an object
                    scriptObject = config.task;
                    scriptContent = config.task;
                }

                // Prepare combined message before session creation
                const scriptContentStr = typeof scriptContent === 'string' ? scriptContent : JSON.stringify(scriptContent);
                const isLocalIncomingCall = scriptContentStr && (
                    scriptContentStr.includes('INCOMING CALL') ||
                    scriptContentStr.includes('Incoming Calls') ||
                    scriptContentStr.includes('incoming call')
                );

                const localPrepareMessage = isLocalIncomingCall
                    ? (AI_PREPARE_MESSAGE_INCOMING || "IMPORTANT: This is an INCOMING call - THEY are calling YOU. Wait for the caller to speak first.")
                    : (AI_PREPARE_MESSAGE_OUTBOUND || AI_PREPARE_MESSAGE || "IMPORTANT: This is an OUTBOUND call - YOU are calling THEM. Speak first immediately.");

                const localCallType = isLocalIncomingCall ? "INCOMING" : "OUTBOUND";

                // Create system message BEFORE session creation
                const languageInstructions = config.language === 'en' ? 'ALWAYS SPEAK ENGLISH' :
                    config.language === 'es' ? 'HABLA SOLO EN ESPAÑOL' :
                    config.language === 'cz' ? 'VŽDY MLUVTE ČESKY' : 'ALWAYS SPEAK ENGLISH';

                const specificTaskInstructions = config.language === 'en' ? 'Follow the task_script exactly and provide helpful assistance' :
                    config.language === 'es' ? 'Sigue exactamente el task_script y proporciona asistencia útil' :
                    config.language === 'cz' ? 'Postupujte přesně podle task_script a poskytněte užitečnou pomoc' : 'Follow the task_script exactly and provide helpful assistance';

                const voiceType = config.language === 'en' ? 'female, american accent' :
                    config.language === 'es' ? 'female, mexican accent' :
                    config.language === 'cz' ? 'female, czech accent' : 'female, american accent';

                const languageEnforcement = config.language === 'en' ?
                    `<CRITICAL_LANGUAGE_INSTRUCTION>ALWAYS SPEAK ENGLISH! Never speak other languages. You MUST respond only in English language.</CRITICAL_LANGUAGE_INSTRUCTION>` :
                    config.language === 'cz' ?
                    `<CRITICAL_LANGUAGE_INSTRUCTION>VŽDY MLUVTE ČESKY! Nikdy nemluvte anglicky nebo jiným jazykem. MUSÍTE odpovídat pouze v českém jazyce.</CRITICAL_LANGUAGE_INSTRUCTION>` :
                    config.language === 'es' ?
                    `<CRITICAL_LANGUAGE_INSTRUCTION>HABLA SOLO EN ESPAÑOL! Never speak English. You MUST respond only in Spanish language.</CRITICAL_LANGUAGE_INSTRUCTION>` :
                    '';

                // According to CAMPAIGN_SCRIPT_POLICY.md: Send FULL campaign script as system instructions
                let localAIInstructions;
                try {
                    console.log('🔍 [LOCAL DEBUG] Script object type:', typeof scriptObject);
                    console.log('🔍 [LOCAL DEBUG] Script object preview:', JSON.stringify(scriptObject).substring(0, 200));

                    // Handle both JSON objects and text strings
                    let localScriptObject;
                    if (typeof scriptObject === 'string') {
                        // If it's a text string, create a minimal campaign object
                        console.log('📝 [LOCAL DEBUG] Creating campaign object from text string');
                        localScriptObject = {
                            agentPersona: {
                                name: 'AI Assistant',
                                role: 'Customer Service Representative',
                                tone: 'Professional and helpful'
                            },
                            script: {
                                instructions: scriptObject
                            },
                            callContext: {
                                purpose: `Local testing session with ${config.targetName || 'user'} at ${config.targetPhoneNumber || 'unknown number'}`,
                                testingMode: true
                            }
                        };
                    } else {
                        // If it's already an object, use it directly
                        console.log('📋 [LOCAL DEBUG] Using existing script object');
                        localScriptObject = { ...scriptObject };
                        if (!localScriptObject.callContext) {
                            localScriptObject.callContext = {};
                        }
                        localScriptObject.callContext.purpose = `Local testing session with ${config.targetName || 'user'} at ${config.targetPhoneNumber || 'unknown number'}`;
                        localScriptObject.callContext.testingMode = true;
                    }

                    // Add language requirements if specified
                    if (config.language && localScriptObject.agentPersona) {
                        if (!localScriptObject.agentPersona.behavioralGuidelines) {
                            localScriptObject.agentPersona.behavioralGuidelines = {};
                        }
                        if (config.language === 'cz') {
                            localScriptObject.agentPersona.behavioralGuidelines.languageRequirement = 'MLUVTE POUZE ČESKY! NEVER speak English. You MUST respond only in Czech language.';
                        } else if (config.language === 'es') {
                            localScriptObject.agentPersona.behavioralGuidelines.languageRequirement = 'HABLA SOLO EN ESPAÑOL! Never speak English. You MUST respond only in Spanish language.';
                        }
                    }

                    // Send the FULL campaign script as system instructions (not extracted components)
                    localAIInstructions = `CAMPAIGN SCRIPT - Follow this script exactly:\n\n${JSON.stringify(localScriptObject, null, 2)}`;
                    console.log('📋 [LOCAL] Using full campaign script as system instructions (length:', localAIInstructions.length, 'chars)');
                } catch (error) {
                    console.error('❌ Error preparing full campaign script for local testing:', error);
                    // Fallback: create minimal instructions
                    localAIInstructions = `You are testing a campaign script. Follow the script exactly. This is a local testing session.`;
                }

                // Create combined message with AI instructions and prepare message
                const combinedMessage = `${localAIInstructions}\n\n${localPrepareMessage}`;
                console.log(`🎯 [LOCAL] ${localCallType} CALL - Combined message prepared (${combinedMessage.length} chars)`);

                // Store session reference for callback
                let sessionRef = null;

                const session = await geminiClient.live.connect({
                    model: config.model || DEFAULT_GEMINI_MODEL,
                    callbacks: {
                        onopen: function() {
                            console.log('✅ Local Gemini session opened successfully');
                            isSessionActive = true;
                            lastActivityTime = Date.now();

                            // Start keep-alive mechanism to prevent session timeouts
                            startKeepAlive();
                            console.log('🔄 [KEEP-ALIVE] Session heartbeat started');

                            // Initialize Deepgram transcription for better recovery context
                            initializeLocalDeepgram();
                        },

                        onmessage: async (message) => {
                            try {
                                console.log('📨 [LOCAL] Received Gemini message:', message.type || 'unknown type');
                                console.log('🔍 [LOCAL] Full message structure:', JSON.stringify(message, null, 2).substring(0, 1000));

                                const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;

                                if (audio && connection.readyState === 1) {
                                    console.log('🔊 [LOCAL] Sending audio response to client, size:', audio.data?.length || 0);

                                    // Update activity tracking for keep-alive
                                    lastActivityTime = Date.now();

                                    // Send audio back to client
                                    connection.send(JSON.stringify({
                                        type: 'audio',
                                        audio: audio.data
                                    }));
                                    console.log('✅ [LOCAL] Audio sent successfully, connection still open:', connection.readyState === 1);
                                } else if (message.serverContent) {
                                    console.log('📝 [LOCAL] Received non-audio message from Gemini:', JSON.stringify(message.serverContent).substring(0, 200));

                                    // Capture AI text response for conversation history
                                    const aiText = message.serverContent?.modelTurn?.parts?.[0]?.text;
                                    if (aiText) {
                                        conversationHistory.push({
                                            role: 'model',
                                            content: aiText,
                                            timestamp: new Date().toISOString()
                                        });
                                        console.log('📝 [CONTEXT] AI response added to conversation history:', aiText.substring(0, 100) + '...');

                                        // Check if this is a summary response (after user requested stop)
                                        const isSummaryResponse = aiText.toLowerCase().includes('summary') ||
                                                                aiText.toLowerCase().includes('main topics') ||
                                                                aiText.toLowerCase().includes('key points') ||
                                                                aiText.length > 200;

                                        if (isSummaryResponse && !isSessionActive) {
                                            console.log('📋 [SUMMARY] Received comprehensive summary from AI - sending to client and ending session');
                                            console.log(`📊 [SUMMARY] Summary length: ${aiText.length} characters`);

                                            // Send summary to client with additional metadata
                                            if (connection.readyState === 1) {
                                                connection.send(JSON.stringify({
                                                    type: 'summary',
                                                    summary: aiText,
                                                    conversationLength: conversationHistory.length,
                                                    speechTranscriptLength: speechTranscript.length,
                                                    sessionDuration: Math.round((Date.now() - (sessionConfig?.startTime || Date.now())) / 1000),
                                                    timestamp: new Date().toISOString()
                                                }));
                                                console.log('✅ [SUMMARY] Summary sent to client with metadata');
                                            }

                                            // End session after summary is delivered
                                            setTimeout(() => {
                                                console.log('🔚 [SUMMARY] Ending session after summary delivery');
                                                endSession();
                                            }, 3000); // Give 3 seconds for summary to be sent and processed
                                        }
                                    }

                                    if (message.serverContent.turnComplete) {
                                        console.log('🎯 [LOCAL] Turn complete - waiting for next user input, keeping session alive');
                                    }
                                } else if (message.setupComplete) {
                                    console.log('✅ [LOCAL] Gemini setup complete - session ready for conversation');
                                    // Session is now ready - don't close it!
                                } else if (message.goAway) {
                                    console.log('⚠️ [LOCAL] Gemini session ending - goAway received:', message.goAway);
                                    console.log('🔄 [LOCAL] Session will close in:', message.goAway.timeLeft);
                                    console.log('🔄 [LOCAL] IGNORING goAway - keeping session alive until user manually stops');

                                    // REQUIREMENT A: Keep session alive as long as call/test is active
                                    // Don't mark session as inactive - ignore Gemini's termination request
                                    // Prepare for automatic recovery if session actually closes
                                    console.log('✅ [LOCAL] Session kept alive - user controls session lifecycle, not Gemini');
                                    console.log('🔄 [LOCAL] Preparing recovery context in case session drops');

                                    // Save current conversation state for potential recovery
                                    if (conversationHistory.length > 0) {
                                        console.log(`💾 [LOCAL] Saving conversation context (${conversationHistory.length} messages) for recovery`);
                                    }
                                } else {
                                    console.log('⚠️ [LOCAL] Received message with no serverContent:', JSON.stringify(message).substring(0, 500));
                                }
                            } catch (error) {
                                console.error('❌ Error processing Gemini message:', error);
                            }
                        },

                        onerror: (error) => {
                            console.error('❌ Local Gemini session error:', error);
                            console.log('🔄 [RECOVERY] Gemini session failed - preparing for automatic recovery');

                            // REQUIREMENT B: If Gemini API drops for technical reasons, reconnect with context
                            isSessionActive = false;
                            geminiSession = null; // Clear failed session

                            // Don't send error to frontend - we'll recover automatically
                            console.log('✅ [RECOVERY] Session marked for recovery - will reconnect on next audio input');
                        },

                        onclose: () => {
                            console.log('🔒 Local Gemini session closed - investigating why');
                            console.log('🔍 [LOCAL] Session was active:', isSessionActive);
                            console.log('🔍 [LOCAL] Connection state:', connection.readyState);
                            console.log('🔍 [LOCAL] Conversation history length:', conversationHistory.length);

                            // REQUIREMENT B: If Gemini API drops for technical reasons, prepare for recovery
                            if (isSessionActive && connection.readyState === 1) {
                                console.log('🚑 [LOCAL] Gemini session dropped unexpectedly - preparing automatic recovery');
                                console.log('💾 [LOCAL] Preserving conversation context for recovery');

                                // Mark session as needing recovery but don't end it
                                geminiSession = null; // Clear the closed session reference
                                // Keep isSessionActive = true so recovery can happen on next audio input
                                console.log('✅ [LOCAL] Session marked for automatic recovery with context preservation');
                            } else {
                                console.log('🔚 [LOCAL] Session closed normally (user stopped or connection ended)');
                                isSessionActive = false;
                            }
                        }
                    },
                    config: {
                        responseModalities: [Modality.AUDIO],
                        speechConfig: {
                            voiceConfig: {
                                prebuiltVoiceConfig: {
                                    voiceName: config.voice || DEFAULT_VOICE
                                }
                            }
                        }
                    }
                });

                // Assign session instance for use in callbacks
                sessionInstance = session;

                // Send initial system message with task using same format as outbound calls
                if (config.task) {
                    console.log(`🎯 [LOCAL DEBUG] Received task type: ${typeof config.task}`);
                    console.log(`🎯 [LOCAL DEBUG] Task content: ${JSON.stringify(config.task).substring(0, 500)}...`);

                    // System message and combined message already created before session creation
                    console.log(`🎯 [LOCAL DEBUG] Using pre-created combined message (${combinedMessage.length} chars)`);
                }

                // Store session reference for callback
                sessionRef = session;

                // Send the combined message now that session is created and ready
                if (combinedMessage && session) {
                    console.log(`🎯 [LOCAL] ${localCallType} CALL - Now sending combined system + prepare message`);
                    try {
                        session.sendClientContent({
                            turns: [{
                                role: 'user',
                                parts: [{
                                    text: combinedMessage
                                }]
                            }],
                            turnComplete: true
                        });
                        console.log('📤 Combined campaign system message + prepare message sent to Gemini for local testing');

                        // Inject conversation history if available
                        if (conversationHistory.length > 0) {
                            console.log('🔄 [CONTEXT] Injecting conversation history, messages:', conversationHistory.length);
                            try {
                                for (const historyMessage of conversationHistory) {
                                    await sessionInstance.sendRealtimeInput({
                                        clientContent: {
                                            turns: [{
                                                role: historyMessage.role,
                                                parts: [{
                                                    text: historyMessage.content
                                                }]
                                            }],
                                            turnComplete: true
                                        }
                                    });
                                }
                                console.log('✅ [CONTEXT] Conversation history injected successfully');
                            } catch (historyError) {
                                console.error('❌ [CONTEXT] Error injecting conversation history:', historyError);
                            }
                        }

                    } catch (sendError) {
                        console.error('❌ Error sending combined message to local Gemini session:', sendError);
                    }
                }

                return session;
            } catch (error) {
                console.error('❌ Error initializing local Gemini session:', error);
                throw error;
            }
        };

        // Handle incoming messages from client
        connection.on('message', async (message) => {
            try {
                const data = JSON.parse(message.toString());
                console.log('📨 [LOCAL] Received WebSocket message type:', data.type);
                console.log('🎯 [LOCAL DEBUG] Full message data:', JSON.stringify(data).substring(0, 500) + '...');

                if (data.type === 'config') {
                    console.log('⚙️ [LOCAL DEBUG] Processing config message');
                    // Store configuration and initialize session
                    sessionConfig = data;
                    sessionConfig.startTime = Date.now(); // Track session start time for summary metadata
                    geminiSession = await initializeLocalGeminiSession(sessionConfig);

                } else if (data.type === 'audio') {
                    // REQUIREMENT B: Auto-reconnect with conversation context if session dropped
                    if (!geminiSession && isSessionActive) {
                        console.log('🔄 [RECOVERY] Gemini session dropped but call/test still active - reconnecting with conversation context');
                        console.log(`💾 [RECOVERY] Preserving conversation history: ${conversationHistory.length} messages`);

                        try {
                            // Recreate session with full conversation context
                            geminiSession = await initializeLocalGeminiSession(sessionConfig);

                            // Send conversation context to recovered session
                            if (conversationHistory.length > 0 || speechTranscript.length > 0) {
                                console.log('📤 [RECOVERY] Sending enhanced conversation context to recovered session');
                                console.log(`📊 [RECOVERY] Context includes: ${conversationHistory.length} conversation messages, ${speechTranscript.length} speech transcripts`);

                                // Format conversation history with timestamps
                                const formattedHistory = conversationHistory.map(msg =>
                                    `[${msg.timestamp || 'Unknown time'}] ${msg.role.toUpperCase()}: ${msg.content}`
                                ).join('\n');

                                // Format speech transcripts for additional context
                                const formattedSpeech = speechTranscript.map(speech =>
                                    `[${speech.timestamp}] USER SPEECH (${Math.round(speech.confidence * 100)}% confidence): ${speech.text}`
                                ).join('\n');

                                let contextMessage = `[SYSTEM RECOVERY] Session was interrupted due to technical issues. Here is the complete conversation context to continue seamlessly:

CONVERSATION HISTORY:
${formattedHistory}`;

                                if (speechTranscript.length > 0) {
                                    contextMessage += `

DETAILED SPEECH TRANSCRIPTS:
${formattedSpeech}`;
                                }

                                contextMessage += `

INSTRUCTIONS: Please continue the conversation naturally from where it left off. Do not mention the technical interruption. Resume as if nothing happened. Use the speech transcripts for the most accurate understanding of what the user said.`;

                                await geminiSession.sendClientContent({
                                    turns: [{
                                        role: 'user',
                                        parts: [{
                                            text: contextMessage
                                        }]
                                    }],
                                    turnComplete: true
                                });
                                console.log('✅ [RECOVERY] Enhanced conversation context sent to recovered session');
                            }

                            console.log('✅ [RECOVERY] Session reconnected successfully with full context preservation');
                        } catch (error) {
                            console.error('❌ [RECOVERY] Failed to reconnect session:', error);
                            // Don't return - let the error handling below deal with it
                        }
                    } else if (!isSessionActive) {
                        console.log('⏸️ [LOCAL] Session inactive - ignoring audio (user stopped or session ended)');
                        return;
                    }

                    // Forward audio to Gemini session (existing or recovered)
                    try {
                        geminiSession.sendRealtimeInput({
                            media: data.media
                        });
                        console.log('📤 [LOCAL] Audio forwarded to Gemini session');

                        // Send audio to Deepgram for transcription
                        if (deepgramConnection && data.media?.data) {
                            try {
                                const audioBuffer = Buffer.from(data.media.data, 'base64');
                                deepgramConnection.send(audioBuffer);
                                console.log('📤 [DEEPGRAM] Audio sent for transcription');
                            } catch (dgError) {
                                console.warn('⚠️ [DEEPGRAM] Error sending audio for transcription:', dgError);
                            }
                        }

                        // Update activity tracking for keep-alive
                        lastActivityTime = Date.now();

                        // Track user input for conversation context (will be updated by Deepgram with actual speech)
                        conversationHistory.push({
                            role: 'user',
                            content: '[User spoke - audio input]',
                            timestamp: new Date().toISOString()
                        });

                    } catch (error) {
                        console.error('❌ Error sending audio to Gemini:', error);
                        console.log('🔄 [RECOVERY] Audio send failed - marking session for recovery');
                        isSessionActive = false;
                        geminiSession = null;
                    }

                } else if (data.type === 'stop') {
                    // REQUIREMENT C & D: User manually stopped - generate summary and end session
                    console.log('🛑 [STOP] User manually stopped call/test - generating session summary');
                    console.log(`📊 [STOP] Conversation history length: ${conversationHistory.length} messages`);
                    console.log(`📊 [STOP] Speech transcript length: ${speechTranscript.length} transcripts`);

                    if (geminiSession && (conversationHistory.length > 0 || speechTranscript.length > 0)) {
                        try {
                            // Mark session as stopping to help with summary detection
                            isSessionActive = false;

                            // Generate comprehensive conversation summary
                            const summaryPrompt = `Please provide a comprehensive summary of our conversation. Include:
1. Main topics discussed
2. Key points or decisions made
3. Any action items or next steps
4. Overall tone and outcome of the conversation

Keep the summary concise but informative.`;

                            console.log('📋 [SUMMARY] Requesting comprehensive summary from Gemini');
                            await geminiSession.sendClientContent({
                                turns: [{
                                    role: 'user',
                                    parts: [{
                                        text: summaryPrompt
                                    }]
                                }],
                                turnComplete: true
                            });
                            console.log('✅ [SUMMARY] Summary request sent successfully');

                            // Set a timeout to end session if summary takes too long
                            setTimeout(() => {
                                console.log('⏰ [SUMMARY] Summary timeout - ending session');
                                endSession();
                            }, 15000); // 15 second timeout for summary

                        } catch (error) {
                            console.error('❌ [SUMMARY] Error requesting summary:', error);
                            // End session immediately if summary fails
                            endSession();
                        }
                    } else {
                        console.log('📝 [STOP] No conversation to summarize - ending session immediately');
                        endSession();
                    }
                }
            } catch (error) {
                console.error('❌ Error processing local audio message:', error);
            }
        });

        // Initialize Deepgram transcription for local testing
        function initializeLocalDeepgram() {
            if (!deepgramClient) {
                console.warn('⚠️ [LOCAL] Deepgram not available - enhanced recovery context disabled');
                return;
            }

            try {
                deepgramConnection = deepgramClient.listen.live({
                    model: 'nova-2',
                    language: 'en',
                    smart_format: true,
                    interim_results: false,
                    utterance_end_ms: 1000,
                    vad_events: true
                });

                deepgramConnection.on('open', () => {
                    console.log('🎤 [LOCAL] Deepgram transcription connection opened for enhanced recovery');
                });

                deepgramConnection.on('results', (data) => {
                    try {
                        const transcript = data.channel?.alternatives?.[0]?.transcript;
                        if (transcript && transcript.trim()) {
                            speechTranscript.push({
                                text: transcript,
                                timestamp: new Date().toISOString(),
                                confidence: data.channel?.alternatives?.[0]?.confidence || 0
                            });
                            console.log('📝 [DEEPGRAM] User speech transcribed:', transcript);

                            // Update conversation history with actual speech content
                            if (conversationHistory.length > 0 &&
                                conversationHistory[conversationHistory.length - 1].content === '[User spoke - audio input]') {
                                conversationHistory[conversationHistory.length - 1].content = transcript;
                                console.log('✅ [DEEPGRAM] Updated conversation history with speech transcript');
                            }
                        }
                    } catch (error) {
                        console.warn('⚠️ [LOCAL] Deepgram transcript processing error:', error);
                    }
                });

                deepgramConnection.on('error', (error) => {
                    console.error('❌ [LOCAL] Deepgram transcription error:', error);
                });

                deepgramConnection.on('close', () => {
                    console.log('🔌 [LOCAL] Deepgram transcription connection closed');
                });

                console.log('✅ [LOCAL] Deepgram transcription initialized for enhanced recovery context');
            } catch (error) {
                console.error('❌ [LOCAL] Failed to initialize Deepgram transcription:', error);
            }
        }

        // Keep-alive mechanism to prevent session timeouts
        function startKeepAlive() {
            // Clear any existing keep-alive
            if (keepAliveInterval) {
                clearInterval(keepAliveInterval);
            }

            keepAliveInterval = setInterval(() => {
                if (isSessionActive && geminiSession) {
                    const timeSinceActivity = Date.now() - lastActivityTime;
                    console.log(`💓 [KEEP-ALIVE] Session heartbeat - time since last activity: ${Math.round(timeSinceActivity/1000)}s`);

                    // Send a minimal keep-alive message every 5 minutes if no activity
                    if (timeSinceActivity > 300000) { // 5 minutes
                        try {
                            console.log('🔄 [KEEP-ALIVE] Sending heartbeat to maintain session');
                            // Send a minimal message to keep session alive
                            geminiSession.sendClientContent({
                                turns: [{
                                    role: 'user',
                                    parts: [{
                                        text: '[SYSTEM HEARTBEAT - Please acknowledge briefly]'
                                    }]
                                }],
                                turnComplete: true
                            });
                            lastActivityTime = Date.now();
                        } catch (error) {
                            console.error('❌ [KEEP-ALIVE] Error sending heartbeat:', error);
                        }
                    }
                } else {
                    console.log('⏹️ [KEEP-ALIVE] Session inactive - stopping heartbeat');
                    clearInterval(keepAliveInterval);
                    keepAliveInterval = null;
                }
            }, 60000); // Check every minute
        }

        // Helper function to end session cleanly
        function endSession() {
            console.log('🔚 [END] Ending session cleanly');
            isSessionActive = false;

            // Stop keep-alive mechanism
            if (keepAliveInterval) {
                clearInterval(keepAliveInterval);
                keepAliveInterval = null;
                console.log('⏹️ [KEEP-ALIVE] Heartbeat stopped');
            }

            // Close Deepgram transcription
            if (deepgramConnection) {
                try {
                    deepgramConnection.finish();
                    console.log('🔌 [DEEPGRAM] Transcription connection closed');
                } catch (error) {
                    console.warn('⚠️ [DEEPGRAM] Error closing transcription:', error);
                }
                deepgramConnection = null;
            }

            if (geminiSession) {
                try {
                    geminiSession.close();
                } catch (error) {
                    console.error('❌ Error closing Gemini session:', error);
                }
                geminiSession = null;
            }

            conversationHistory = [];
            speechTranscript = [];
            sessionConfig = null;
            console.log('✅ [END] Session ended and cleaned up');
        }

        // Handle connection close
        connection.on('close', (code, reason) => {
            console.log('🔒 Local audio session disconnected');
            console.log('🔍 [LOCAL] Close code:', code, 'reason:', reason?.toString());
            console.log('🔍 [LOCAL] Session was active:', isSessionActive);
            console.log('🔍 [LOCAL] Gemini session exists:', !!geminiSession);

            // Analyze close codes to understand why frontend disconnected
            if (code === 1001) {
                console.log('🔍 [LOCAL] Frontend went away (1001) - likely page refresh, navigation, or frontend error');
            } else if (code === 1006) {
                console.log('🔍 [LOCAL] Abnormal closure (1006) - likely network issue or frontend crash');
            } else if (code === 1000) {
                console.log('🔍 [LOCAL] Normal closure (1000) - frontend intentionally closed connection');
            } else {
                console.log('🔍 [LOCAL] Unexpected close code:', code);
            }

            // DON'T automatically close Gemini session - let it stay open for reconnection
            console.log('⚠️ [LOCAL] NOT closing Gemini session automatically - keeping it alive for potential reconnection');

            // Only close if explicitly requested or if session is in error state
            if (geminiSession && !isSessionActive) {
                console.log('🔒 [LOCAL] Closing Gemini session because session is inactive');
                try {
                    geminiSession.close();
                } catch (error) {
                    console.error('❌ Error closing Gemini session:', error);
                }
            }
        });

        // Handle connection errors
        connection.on('error', (error) => {
            console.error('❌ Local audio session error:', error);
            if (geminiSession) {
                try {
                    geminiSession.close();
                } catch (closeError) {
                    console.error('❌ Error closing Gemini session after error:', closeError);
                }
            }
        });
    });
});

// --- API Routes (Registered directly on fastify instance) ---

// --- Endpoint to Get Campaign Scripts ---
fastify.get('/get-campaign-script/:id', async (request, reply) => {
    const campaignId = request.params.id;
    // Allow campaign IDs 1-12
    const validCampaignIds = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'];
    if (!validCampaignIds.includes(campaignId)) {
        reply.status(400).send({ error: 'Invalid campaign ID. Must be 1-12.' }); return;
    }
    
    // Load .json files from public directory
    const publicDir = path.join(__dirname, 'call-center-frontend', 'public');
    const jsonFilePath = path.join(publicDir, `campaign${campaignId}.json`);
    
    try {
        const fileContent = await readFile(jsonFilePath, 'utf8');
        const jsonData = JSON.parse(fileContent);
        reply.send(jsonData);
    } catch (error) {
        if (error.code === 'ENOENT') {
            reply.status(404).send({ error: `Campaign script ${campaignId}.json not found.` });
        } else if (error instanceof SyntaxError) {
            reply.status(400).send({ error: `Campaign script ${campaignId}.json contains invalid JSON.` });
        } else {
            reply.status(500).send({ error: 'Failed to read campaign script.' });
        }
        console.error(`❌ Error handling /get-campaign-script/${campaignId}:`, error);
    }
});

// --- Endpoint to Update Session Config (Voice, Script, Language, Model) ---
fastify.post('/update-session-config', async (request, reply) => {
  try {
    const { task, voice, model, targetName, targetPhoneNumber, outputLanguage } = request.body;

    // Input validation and sanitization
    if (!task) {
        reply.status(400).send({ error: 'Missing required parameter: task (script content)' }); return;
    }

    // Validate task object (already parsed from JSON request body)
    let sanitizedTask = task;
    if (typeof task === 'string') {
        // If task is a string, parse it as JSON
        sanitizedTask = SecurityUtils.validateJSON(task, 50240); // 50KB max for campaign scripts
        if (!sanitizedTask) {
            reply.status(400).send({ error: 'Invalid JSON format for task script or script too large.' }); return;
        }
    } else if (typeof task === 'object' && task !== null) {
        // Task is already an object, validate its size
        const taskString = JSON.stringify(task);
        if (taskString.length > 50240) {
            reply.status(400).send({ error: 'Task script too large (max 50KB).' }); return;
        }
        sanitizedTask = task;
    } else {
        reply.status(400).send({ error: 'Invalid task format - must be object or JSON string.' }); return;
    }

    const sanitizedTargetName = targetName ? SecurityUtils.validateText(targetName, 100) : null;
    const sanitizedTargetPhone = targetPhoneNumber ? SecurityUtils.validatePhoneNumber(targetPhoneNumber) : null;
    const sanitizedVoice = voice ? SecurityUtils.validateText(voice, 50) : null;
    const sanitizedModel = model ? SecurityUtils.validateText(model, 100) : null;
    const sanitizedLanguage = outputLanguage ? SecurityUtils.validateText(outputLanguage, 10) : null;

    // Validate phone number format if provided
    if (targetPhoneNumber && !sanitizedTargetPhone) {
        reply.status(400).send({ error: 'Invalid phone number format. Use international format (+**********).' }); return;
    }

    // Validate task is proper JSON structure and extract script content
    // According to CAMPAIGN_SCRIPT_POLICY.md: task should be the full campaign script
    let scriptObject = sanitizedTask; // Full campaign script object
    let scriptContent = sanitizedTask;

    // If the task contains a 'script' property, extract it for content but keep full object for AI instructions
    if (sanitizedTask && typeof sanitizedTask === 'object' && sanitizedTask.script) {
        scriptContent = sanitizedTask.script; // Just the conversation flow
        // scriptObject remains the full campaign script with agentPersona, etc.
    }

    // --- Date/Time Context ---
    let dateTimeContext = "Current date and time context unavailable.";
    if (sanitizedTargetPhone) {
        try {
            const phoneNumber = parsePhoneNumber(sanitizedTargetPhone);
            if (phoneNumber && phoneNumber.country) {
                const timezoneData = getTimezone(phoneNumber.country);
                if (timezoneData) {
                    const now = new Date();
                    const formatter = new Intl.DateTimeFormat('en-US', {
                        dateStyle: 'full',
                        timeStyle: 'short',
                        timeZone: timezoneData.utc[0],
                        hour12: true
                    });
                    dateTimeContext = `Current date and time for the recipient is approximately ${formatter.format(now)} (${timezoneData.utc[0]}).`;
                } else {
                     dateTimeContext = `Could not determine timezone for ${phoneNumber.country}. Current UTC time is ${new Date().toUTCString()}.`;
                }
            } else {
                 dateTimeContext = `Could not parse phone number country. Current UTC time is ${new Date().toUTCString()}.`;
            }
        } catch (e) {
            console.error(`❌ Error getting timezone for ${targetPhoneNumber}:`, e);
            dateTimeContext = `Error determining timezone. Current UTC time is ${new Date().toUTCString()}.`;
        }
    } else {
         dateTimeContext = `Target phone number not provided. Current UTC time is ${new Date().toUTCString()}.`;
    }
    // --- End Date/Time Context ---

    // --- Voice/Language Configuration ---
    let voiceType = "female, california accent"; // Default English
    let languageInstructions = "ALWAYS SPEAK ENGLISH IN YOUR ACCENT"; // Default English rule
    let specificTaskInstructions = "Follow the task_script precisely."; // Default English rule

    // Adjust based on sanitized outputLanguage parameter
    if (sanitizedLanguage === 'es') {
        voiceType = "female, spanish accent";
        languageInstructions = "ALWAYS SPEAK SPANISH";
        specificTaskInstructions = "Sigue el task_script precisamente.";
    } else if (sanitizedLanguage === 'cz') {
        voiceType = "female, czech accent";
        languageInstructions = "ALWAYS SPEAK CZECH - NEVER USE ENGLISH. RESPOND ONLY IN CZECH LANGUAGE. ČESKY MLUVTE VŽDY.";
        specificTaskInstructions = "Postupujte přesně podle task_script. Mluvte pouze česky.";
    }
    // Add more 'else if' blocks for other languages as needed

    // According to CAMPAIGN_SCRIPT_POLICY.md: NO SYSTEM PROMPTS
    // Extract AI instructions directly from campaign script
    try {
        // Add language context to the campaign script if needed
        let enhancedScript = { ...scriptObject };

        // Add language enforcement to agent persona if specified
        if (sanitizedLanguage) {
            if (!enhancedScript.agentPersona) enhancedScript.agentPersona = {};
            if (!enhancedScript.agentPersona.behavioralGuidelines) enhancedScript.agentPersona.behavioralGuidelines = {};

            if (sanitizedLanguage === 'cz') {
                enhancedScript.agentPersona.behavioralGuidelines.languageRequirement = 'MLUVTE POUZE ČESKY! NEVER speak English. You MUST respond only in Czech language. Česky mluvte vždy!';
            } else if (sanitizedLanguage === 'es') {
                enhancedScript.agentPersona.behavioralGuidelines.languageRequirement = 'HABLA SOLO EN ESPAÑOL! Never speak English. You MUST respond only in Spanish language.';
            }
        }

        // Add call context if not present
        if (!enhancedScript.callContext) {
            enhancedScript.callContext = {
                purpose: `Outbound call to ${sanitizedTargetName || 'customer'} at ${sanitizedTargetPhone || 'unknown number'}`,
                callDirection: 'agent_calls_customer',
                dateTimeContext: dateTimeContext
            };
        }

        // According to CAMPAIGN_SCRIPT_POLICY.md: Send FULL campaign script as system instructions
        const aiInstructions = `CAMPAIGN SCRIPT - Follow this script exactly:\n\n${JSON.stringify(enhancedScript, null, 2)}`;
        console.log('📋 [OUTBOUND] Using full campaign script as system instructions (length:', aiInstructions.length, 'chars)');

        // Store the AI instructions instead of system message
        nextCallConfig.aiInstructions = aiInstructions;
        nextCallConfig.voice = getValidGeminiVoice(sanitizedVoice) || DEFAULT_VOICE;
        nextCallConfig.model = getValidGeminiModel(sanitizedModel) || DEFAULT_GEMINI_MODEL;
        nextCallConfig.targetName = sanitizedTargetName;
        nextCallConfig.targetPhoneNumber = sanitizedTargetPhone;
        console.log(`⚙️ Session config updated for next call. Model: ${nextCallConfig.model}, Voice: ${nextCallConfig.voice}. Target: ${sanitizedTargetName} (${sanitizedTargetPhone}). Language: ${sanitizedLanguage || 'default (en)'}`);

        // Debug: Log the campaign script content
        console.log(`🎯 [DEBUG] Campaign script object type: ${typeof scriptObject}`);
        console.log(`🎯 [DEBUG] Campaign script preview: ${JSON.stringify(scriptObject).substring(0, 300)}...`);
        console.log(`🎯 [DEBUG] AI instructions length: ${aiInstructions.length}`);

        // Debug: Log language-specific information
        if (sanitizedLanguage === 'cz') {
            console.log(`🇨🇿 CZECH LANGUAGE CONFIGURATION:`);
            console.log(`Language Instructions: ${languageInstructions}`);
            console.log(`Task Instructions: ${specificTaskInstructions}`);
            console.log(`Voice Type: ${voiceType}`);
        }

    } catch (error) {
        console.error('❌ Error extracting AI instructions from campaign script:', error);
        reply.status(500).send({ error: 'Failed to process campaign script according to policy' });
        return;
    }

    return { success: true };
  } catch (error) {
    console.error('❌ Error updating session config:', error);
    reply.status(500).send({ error: 'Failed to update session config' });
  }
});

// --- Endpoint to Get Server Status and Performance ---
fastify.get('/status', async (request, reply) => {
    try {
        const status = {
            server: 'Twilio Gemini Live API Server',
            status: 'running',
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            activeConnections: activeConnections.size,
            currentModel: CURRENT_GEMINI_MODEL,
            defaultModel: DEFAULT_GEMINI_MODEL,
            defaultVoice: DEFAULT_VOICE,
            publicUrl: PUBLIC_URL,
            timestamp: new Date().toISOString(),
            version: '2.0.0-gemini'
        };
        reply.send(status);
    } catch (error) {
        console.error('❌ Error getting server status:', error);
        reply.status(500).send({ error: 'Failed to get server status' });
    }
});

// --- Endpoint to Get Available Voices ---
fastify.get('/available-voices', async (request, reply) => {
    try {
        const voiceInfo = {
            defaultVoice: DEFAULT_VOICE,
            currentVoice: nextCallConfig.voice || DEFAULT_VOICE,
            availableVoices: AVAILABLE_GEMINI_VOICES,
            voiceMapping: VOICE_MAPPING,
            voiceSelectionEnabled: VOICE_SELECTION_ENABLED,
            configurationSource: {
                defaultVoice: process.env.GEMINI_DEFAULT_VOICE ? 'environment' : 'hardcoded'
            },
            description: "Available Gemini Live API voices with characteristics and OpenAI voice mappings"
        };
        reply.send(voiceInfo);
    } catch (error) {
        console.error('❌ Error getting available voices:', error);
        reply.status(500).send({ error: 'Failed to get available voices' });
    }
});

// --- Endpoint to Set Voice ---
fastify.post('/set-voice', async (request, reply) => {
    try {
        if (!VOICE_SELECTION_ENABLED) {
            return reply.status(403).send({
                error: 'Voice selection is disabled',
                message: 'Set GEMINI_VOICE_SELECTION_ENABLED=true in .env to enable voice switching'
            });
        }

        const { voice } = request.body;

        if (!voice) {
            return reply.status(400).send({ error: 'Voice parameter is required' });
        }

        const validatedVoice = getValidGeminiVoice(voice);
        if (validatedVoice !== voice && !VOICE_MAPPING[voice]) {
            return reply.status(400).send({
                error: `Invalid voice '${voice}'`,
                availableVoices: Object.keys(AVAILABLE_GEMINI_VOICES),
                voiceMapping: Object.keys(VOICE_MAPPING),
                fallbackUsed: validatedVoice
            });
        }

        const previousVoice = nextCallConfig.voice || DEFAULT_VOICE;
        nextCallConfig.voice = validatedVoice;

        const voiceInfo = AVAILABLE_GEMINI_VOICES[validatedVoice];
        console.log(`🎤 VOICE CHANGED: '${previousVoice}' → '${validatedVoice}' (${voiceInfo.gender}, ${voiceInfo.characteristics})`);

        reply.send({
            success: true,
            previousVoice,
            currentVoice: validatedVoice,
            voiceCharacteristics: voiceInfo,
            message: `Voice successfully changed to ${validatedVoice}`,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Error setting voice:', error);
        reply.status(500).send({ error: 'Failed to set voice' });
    }
});

// --- Endpoint to Get Available Models ---
fastify.get('/available-models', async (request, reply) => {
    try {
        const modelInfo = {
            defaultModel: DEFAULT_GEMINI_MODEL,
            currentModel: CURRENT_GEMINI_MODEL,
            availableModels: AVAILABLE_GEMINI_MODELS,
            modelSelectionEnabled: MODEL_SELECTION_ENABLED,
            configurationSource: {
                defaultModel: process.env.GEMINI_DEFAULT_MODEL ? 'environment' : 'hardcoded',
                availableModels: process.env.GEMINI_AVAILABLE_MODELS ? 'environment' : 'hardcoded'
            },
            description: "Available Gemini models for Live API"
        };
        reply.send(modelInfo);
    } catch (error) {
        console.error('❌ Error getting available models:', error);
        reply.status(500).send({ error: 'Failed to get available models' });
    }
});

// --- Endpoint to Change Current Model ---
fastify.post('/set-model', async (request, reply) => {
    try {
        if (!MODEL_SELECTION_ENABLED) {
            return reply.status(403).send({
                error: 'Model selection is disabled',
                message: 'Set GEMINI_MODEL_SELECTION_ENABLED=true in .env to enable model switching'
            });
        }

        const { model } = request.body;

        if (!model) {
            return reply.status(400).send({ error: 'Model parameter is required' });
        }

        const validatedModel = getValidGeminiModel(model);
        if (validatedModel !== model) {
            return reply.status(400).send({
                error: `Invalid model '${model}'`,
                availableModels: Object.keys(AVAILABLE_GEMINI_MODELS),
                fallbackUsed: validatedModel
            });
        }

        const previousModel = CURRENT_GEMINI_MODEL;
        CURRENT_GEMINI_MODEL = validatedModel;

        console.log(`🔄 MODEL CHANGED: '${previousModel}' → '${CURRENT_GEMINI_MODEL}'`);
        console.log(`🤖 NEW ACTIVE MODEL: ${CURRENT_GEMINI_MODEL} (${AVAILABLE_GEMINI_MODELS[CURRENT_GEMINI_MODEL]?.name || 'Unknown'})`);

        reply.send({
            success: true,
            previousModel,
            currentModel: CURRENT_GEMINI_MODEL,
            message: `Model successfully changed to ${CURRENT_GEMINI_MODEL}`,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('❌ Error setting model:', error);
        reply.status(500).send({ error: 'Failed to set model' });
    }
});

// --- Endpoint to Make Outbound Call ---
fastify.route({
  method: 'POST',
  url: '/make-call',
  schema: {
    body: {
      type: 'object',
      required: ['to', 'from'],
      properties: {
        to: { type: 'string' },
        from: { type: 'string' },
        task: { type: 'string' },
        voice: { type: 'string' },
        model: { type: 'string' },
        targetName: { type: 'string' },
        targetPhoneNumber: { type: 'string' },
        outputLanguage: { type: 'string' }
      }
    }
  },
  handler: async (request, reply) => {
    const { to, from, task, voice, model, targetName, targetPhoneNumber, outputLanguage } = request.body;

    // Input validation and sanitization
    const sanitizedTo = SecurityUtils.validatePhoneNumber(to);
    const sanitizedFrom = SecurityUtils.validatePhoneNumber(from);

    if (!sanitizedTo) {
        reply.status(400).send({ error: 'Invalid "to" phone number format. Use international format (+**********).' });
        return;
    }

    if (!sanitizedFrom) {
        reply.status(400).send({ error: 'Invalid "from" phone number format. Use international format (+**********).' });
        return;
    }

    const client = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);

    // If task/voice/model/target/language provided in this request, update nextCallConfig immediately
    if (task) {
        console.log("🔧 Updating nextCallConfig from /make-call request...");
        await fastify.inject({
            method: 'POST',
            url: '/update-session-config',
            payload: { task, voice, model, targetName, targetPhoneNumber, outputLanguage }
        });
        console.log("✅ nextCallConfig updated via injection.");
    } else {
        console.log("📋 Using existing nextCallConfig for /make-call.");
    }

    try {
      const call = await client.calls.create({
        to: sanitizedTo,
        from: sanitizedFrom,
        url: `${PUBLIC_URL}/incoming-call`,
        statusCallback: `${PUBLIC_URL}/call-status`,
        statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed', 'failed', 'canceled', 'no-answer', 'busy'],
        statusCallbackMethod: 'POST',
        record: true,
        recordingStatusCallback: `${PUBLIC_URL}/recording-status`,
        recordingStatusCallbackMethod: 'POST',
        recordingStatusCallbackEvent: ['completed']
      });
      console.log(`📞 [${call.sid}] Outbound call initiated to ${to}`);
      reply.send({ callSid: call.sid });
    } catch (error) {
      console.error(`❌ Error initiating call to ${to}:`, error);
      reply.status(500).send({ error: 'Failed to initiate call', details: error.message });
    }
  }
});

// --- Endpoint to receive recording status ---
fastify.post('/recording-status', async (request, reply) => {
    const { CallSid: callSid, RecordingUrl: recordingUrl, RecordingStatus: recordingStatus } = request.body;
    console.log(`🎙️ [${callSid}] Recording status update: ${recordingStatus}, URL: ${recordingUrl}`);

    if (recordingStatus === 'completed' && recordingUrl) {
        const infoFilePath = path.join(__dirname, 'data', `${callSid}_info.json`);
        try {
            await mkdir(path.dirname(infoFilePath), { recursive: true });

            let summaryData = {};
            try {
                const existingData = await readFile(infoFilePath, 'utf8');
                summaryData = JSON.parse(existingData);
            } catch (readError) {
                if (readError.code === 'ENOENT') {
                    console.log(`📝 [${callSid}] No existing info file found, creating new one for recording URL.`);
                    summaryData = {
                        callSid: callSid,
                        call_summary: "Summary not yet generated or call ended prematurely.",
                        customer_sentiment: "unknown",
                        status: "unknown",
                        targetName: "unknown",
                        targetPhoneNumber: "unknown",
                        timestamp: new Date().toISOString()
                    };
                } else {
                    throw readError;
                }
            }

            summaryData.recordingUrl = recordingUrl + ".mp3";
            summaryData.recordingTimestamp = new Date().toISOString();

            await writeFile(infoFilePath, JSON.stringify(summaryData, null, 2));
            console.log(`💾 [${callSid}] Recording URL added to ${infoFilePath}`);

        } catch (error) {
            console.error(`❌ [${callSid}] Error updating info file with recording URL:`, error);
        }
    }
    reply.status(200).send();
});

// --- Endpoint to Get Call Results ---
fastify.get('/call-results/:callSid', async (request, reply) => {
    const callSid = request.params.callSid;
    const infoFilePath = path.join(__dirname, 'data', `${callSid}_info.json`);
    console.log(`📊 [${callSid}] Received request for call results.`);

    try {
        await access(infoFilePath);
        const fileContent = await readFile(infoFilePath, 'utf8');
        const jsonData = JSON.parse(fileContent);

        console.log(`✅ [${callSid}] Results file found and parsed. Returning data.`);
        reply.send({ info: jsonData });

    } catch (error) {
        if (error.code === 'ENOENT') {
            console.log(`❓ [${callSid}] Results file not found. Returning 404.`);
            reply.status(404).send({ error: 'Call results not found or not yet available.' });
        } else if (error instanceof SyntaxError) {
            console.error(`❌ [${callSid}] Error parsing results file ${infoFilePath}:`, error);
            reply.status(500).send({ error: 'Failed to parse call results data.' });
        } else {
            console.error(`❌ [${callSid}] Error reading results file ${infoFilePath}:`, error);
            reply.status(500).send({ error: 'Failed to read call results data.' });
        }
    }
});

// Call Status endpoint (Twilio Webhook)
fastify.post('/call-status', async (request, reply) => {
    const { CallSid: callSid, CallStatus: callStatus } = request.body;
    console.log(`📊 [${callSid}] Call status update received: ${callStatus}`);
    const connectionData = activeConnections.get(callSid);

    if (callStatus === 'completed') {
        console.log(`✅ [${callSid}] Call completed via status callback.`);
        if (connectionData && !connectionData.summaryRequested && !connectionData.summaryReceived) {
             console.log(`📝 [${callSid}] Connection found, requesting summary via status callback.`);
             requestSummary(callSid, connectionData);
        } else if (!connectionData) {
            console.warn(`⚠️ [${callSid}] Received 'completed' status, but no active connection found.`);
            const infoFilePath = path.join(__dirname, 'data', `${callSid}_info.json`);
            try { await access(infoFilePath); } catch {
                await saveSummaryInfo(callSid, "Call completed, but connection was already closed.", "neutral", callStatus, null, null);
            }
        } else {
             console.log(`ℹ️ [${callSid}] Received 'completed' status, but summary already requested or received.`);
        }
    } else if (['failed', 'canceled', 'no-answer', 'busy'].includes(callStatus)) {
         console.log(`⚠️ [${callSid}] Call ended with non-completed status: ${callStatus}. Saving placeholder info.`);
         if (connectionData) {
             console.log(`🧹 [${callSid}] Cleaning up connection due to status: ${callStatus}.`);
             if (connectionData.summaryTimeoutId) {
                 console.log(`⏰ [${callSid}] Clearing summary timeout due to status: ${callStatus}.`);
                 clearTimeout(connectionData.summaryTimeoutId);
             }
             // Close Gemini session if open
             if (connectionData.geminiSession) {
                 console.log(`🔒 [${callSid}] Closing Gemini session due to status: ${callStatus}.`);
                 connectionData.geminiSession.close();
             }
             // Close Twilio WS if open
             if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
                 console.log(`🔒 [${callSid}] Closing Twilio WS due to status: ${callStatus}.`);
                 connectionData.twilioWs.close();
             }
             // Save placeholder info
             await saveSummaryInfo(callSid, null, "neutral", callStatus, connectionData.targetName, connectionData.targetPhoneNumber);
             activeConnections.delete(callSid);
             console.log(`🗑️ [${callSid}] Connection data deleted from map.`);
         } else {
              console.warn(`⚠️ [${callSid}] Received terminal status ${callStatus}, but no active connection found.`);
              const infoFilePath = path.join(__dirname, 'data', `${callSid}_info.json`);
              try { await access(infoFilePath); } catch {
                  await saveSummaryInfo(callSid, `Call ended with status: ${callStatus}. Connection already closed.`, "neutral", callStatus, null, null);
              }
         }
    }

    reply.send('OK');
});


// --- Helper Functions ---

// Function to save summary and sentiment
async function saveSummaryInfo(callSid, rawSummaryText, defaultSentiment = "neutral", status = "completed", targetName = null, targetPhoneNumber = null) {
    const infoFilePath = path.join(__dirname, 'data', `${callSid}_info.json`);
    console.log(`💾 [${callSid}] Attempting to save summary info. Status: ${status}, Target: ${targetName}`);
    console.log(`📋 [${callSid}] Raw summary text received: "${rawSummaryText}" (length: ${rawSummaryText?.length || 0})`);

    let summary = "Summary generation failed or was not applicable.";
    let sentiment = defaultSentiment;

    // Basic parsing attempt for sentiment, assuming it might be included
    if (rawSummaryText && status !== 'incomplete' && status !== 'error' && !['failed', 'canceled', 'no-answer', 'busy'].includes(status)) {
        const sentimentMatch = rawSummaryText.match(/sentiment:\s*(positive|neutral|negative)/i);
        if (sentimentMatch && sentimentMatch[1]) {
            sentiment = sentimentMatch[1].toLowerCase();
            summary = rawSummaryText.replace(/sentiment:\s*(positive|neutral|negative)/i, '').trim();
            console.log(`🎭 [${callSid}] Parsed sentiment: ${sentiment}`);
        } else {
            summary = rawSummaryText.trim();
            console.log(`📝 [${callSid}] Sentiment pattern not found in summary text.`);
        }
    } else if (rawSummaryText) {
         summary = rawSummaryText;
         console.log(`📝 [${callSid}] Using provided text for non-completed status summary: "${summary}"`);
    } else if (status === 'failed' || status === 'canceled' || status === 'no-answer' || status === 'busy') {
        summary = `Call ended with status: ${status}. No summary generated.`;
        sentiment = 'neutral';
        console.log(`📝 [${callSid}] Setting placeholder summary for status: ${status}`);
    } else if (status === 'timeout') {
        summary = "Summary generation timed out.";
        sentiment = 'neutral';
        console.log(`📝 [${callSid}] Setting placeholder summary for status: ${status}`);
    } else if (status === 'completed' && targetName) {
        summary = `Call completed with ${targetName}. AI summary generation was not successful, but the call connected and ended normally. Check the recording for details.`;
        sentiment = 'neutral';
        console.log(`📝 [${callSid}] Setting informative fallback for completed call without AI summary`);
    }

    const summaryData = {
        callSid: callSid,
        call_summary: summary,
        customer_sentiment: sentiment,
        status: status,
        targetName: targetName || "Unknown",
        targetPhoneNumber: targetPhoneNumber || "Unknown",
        timestamp: new Date().toISOString()
    };

    try {
        await mkdir(path.dirname(infoFilePath), { recursive: true });
        let existingData = {};
        try {
            const content = await readFile(infoFilePath, 'utf8');
            existingData = JSON.parse(content);
        } catch (readError) {
            if (readError.code !== 'ENOENT') throw readError;
        }
        // Merge new data with existing data (e.g., preserve recordingUrl)
        const finalData = { ...existingData, ...summaryData };

        await writeFile(infoFilePath, JSON.stringify(finalData, null, 2));
        console.log(`✅ [${callSid}] Summary info saved to ${infoFilePath}`);
    } catch (error) {
        console.error(`❌ [${callSid}] Error saving summary info:`, error);
    }
}

// Function to generate post-call summary - FIXED TO WORK AFTER CALL ENDS
async function requestSummary(callSid, connectionData) {
    if (!connectionData || connectionData.summaryRequested || connectionData.summaryReceived) {
        console.log(`⏭️ [${callSid}] Summary request skipped (already requested/received or no connection data).`);
        return;
    }

    console.log(`📝 [${callSid}] Generating post-call summary for target: ${connectionData.targetName}`);
    connectionData.summaryRequested = true;

    // Generate summary from conversation log instead of asking Gemini during/after call
    try {
        let summary = "Call completed";
        let sentiment = "neutral";

        // Use conversation log if available
        if (connectionData.conversationLog && connectionData.conversationLog.length > 0) {
            console.log(`📋 [${callSid}] Generating summary from ${connectionData.conversationLog.length} conversation entries`);

            // Extract key information from conversation
            const userMessages = connectionData.conversationLog.filter(entry => entry.role === 'user').map(entry => entry.content);
            const aiMessages = connectionData.conversationLog.filter(entry => entry.role === 'assistant').map(entry => entry.content);

            // Create a basic summary from the conversation
            const conversationSummary = [];
            if (userMessages.length > 0) {
                conversationSummary.push(`Customer spoke ${userMessages.length} times`);
            }
            if (aiMessages.length > 0) {
                conversationSummary.push(`AI responded ${aiMessages.length} times`);
            }

            // Try to extract key topics or outcomes
            const allText = [...userMessages, ...aiMessages].join(' ').toLowerCase();
            const keyTopics = [];

            // Look for common call center topics
            if (allText.includes('insurance') || allText.includes('claim') || allText.includes('policy')) {
                keyTopics.push('insurance/claims');
            }
            if (allText.includes('appointment') || allText.includes('schedule')) {
                keyTopics.push('scheduling');
            }
            if (allText.includes('payment') || allText.includes('bill') || allText.includes('charge')) {
                keyTopics.push('billing/payment');
            }
            if (allText.includes('problem') || allText.includes('issue') || allText.includes('help')) {
                keyTopics.push('support/assistance');
            }

            // Determine sentiment based on conversation
            if (allText.includes('thank') || allText.includes('great') || allText.includes('good')) {
                sentiment = 'positive';
            } else if (allText.includes('problem') || allText.includes('issue') || allText.includes('wrong')) {
                sentiment = 'negative';
            }

            // Build summary
            summary = `Call completed with ${connectionData.targetName || 'caller'}. `;
            if (conversationSummary.length > 0) {
                summary += conversationSummary.join(', ') + '. ';
            }
            if (keyTopics.length > 0) {
                summary += `Topics discussed: ${keyTopics.join(', ')}. `;
            }

            // Add first few exchanges for context
            if (connectionData.conversationLog.length > 0) {
                const firstExchange = connectionData.conversationLog.slice(0, 2);
                const exchangeText = firstExchange.map(entry => `${entry.role}: ${entry.content.substring(0, 100)}`).join(' | ');
                summary += `Initial exchange: ${exchangeText}`;
            }

        } else if (connectionData.fullTranscript && connectionData.fullTranscript.length > 0) {
            console.log(`📋 [${callSid}] Using full transcript for summary (${connectionData.fullTranscript.length} entries)`);
            summary = `Call completed with ${connectionData.targetName || 'caller'}. Transcript available with ${connectionData.fullTranscript.length} entries.`;
        } else {
            console.log(`📋 [${callSid}] No conversation data available, using basic summary`);
            summary = `Call completed with ${connectionData.targetName || 'caller'}. No detailed conversation data available.`;
        }

        // Save the summary immediately
        await saveSummaryInfo(callSid, summary, sentiment, "completed", connectionData.targetName, connectionData.targetPhoneNumber);
        connectionData.summaryReceived = true;

        console.log(`✅ [${callSid}] Post-call summary generated and saved successfully`);

        // Clean up connection data
        if (connectionData.summaryTimeoutId) {
            clearTimeout(connectionData.summaryTimeoutId);
            connectionData.summaryTimeoutId = null;
        }

        // Close any remaining connections
        if (connectionData.geminiSession) {
            connectionData.geminiSession.close();
        }
        if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
            connectionData.twilioWs.close();
        }

        activeConnections.delete(callSid);
        console.log(`🗑️ [${callSid}] Connection data cleaned up after summary completion`);

    } catch (error) {
        console.error(`❌ [${callSid}] Error generating post-call summary:`, error);
        if (!connectionData.summaryReceived) {
            await saveSummaryInfo(callSid, "Error generating post-call summary.", "neutral", "error", connectionData.targetName, connectionData.targetPhoneNumber);
            connectionData.summaryReceived = true;
            activeConnections.delete(callSid);
            console.log(`🗑️ [${callSid}] Connection data deleted after error saving summary.`);
        }
    }
}

// Start the server
const start = async () => {
    try {
        const port = PORT || 3101;
        await fastify.listen({ port, host: '0.0.0.0' });
        console.log(`🌟 Twilio Gemini Live API Server listening on port ${port}`);
        console.log(`🤖 Current Model: ${CURRENT_GEMINI_MODEL}`);
        console.log(`🤖 Default Model: ${DEFAULT_GEMINI_MODEL}`);
        console.log(`📋 Available Models: ${Object.keys(AVAILABLE_GEMINI_MODELS).join(', ')}`);
        console.log(`🔧 Model Selection: ${MODEL_SELECTION_ENABLED ? 'Enabled' : 'Disabled'}`);
        console.log(`🎤 Default Voice: ${DEFAULT_VOICE} (${AVAILABLE_GEMINI_VOICES[DEFAULT_VOICE]?.characteristics || 'unknown'})`);
        console.log(`🎵 Available Voices: ${Object.keys(AVAILABLE_GEMINI_VOICES).join(', ')}`);
        console.log(`🔧 Voice Selection: ${VOICE_SELECTION_ENABLED ? 'Enabled' : 'Disabled'}`);
        console.log(`⚙️ Configuration Source: ${process.env.GEMINI_DEFAULT_MODEL ? 'Environment' : 'Hardcoded'}`);
        console.log(`🔗 Public URL: ${PUBLIC_URL}`);
        console.log('✅ Server is ready to handle calls!');
    } catch (err) {
        console.error('❌ Error starting server:', err);
        process.exit(1);
    }
};

start();