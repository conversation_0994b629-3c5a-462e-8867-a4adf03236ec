import Fastify from 'fastify';
import dotenv from 'dotenv';
import fastifyFormBody from '@fastify/formbody';
import fastifyWs from '@fastify/websocket';
import fastifyCors from '@fastify/cors';
import fastifyStatic from '@fastify/static';
import fastifyCompress from '@fastify/compress';
import fastifyRateLimit from '@fastify/rate-limit';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import path from 'path';

// Import modular components
import { initializeGeminiClient } from './src/gemini/client.js';
import { voiceManager } from './src/gemini/voice-manager.js';
import { modelManager } from './src/gemini/model-manager.js';
import { ContextManager } from './src/session/context-manager.js';
import { SessionManager } from './src/session/session-manager.js';
import { ConnectionHealthMonitor } from './src/session/health-monitor.js';
import { SessionRecoveryManager } from './src/session/recovery-manager.js';
import { SessionSummaryManager } from './src/session/summary-manager.js';
import { SessionLifecycleManager } from './src/session/lifecycle-manager.js';
import { ScriptManager } from './src/scripts/script-manager.js';
import { AudioProcessor } from './src/audio/audio-processor.js';
import { TranscriptionManager } from './src/audio/transcription-manager.js';
import { registerWebSocketHandlers } from './src/websocket/handlers.js';
import { registerApiRoutes } from './src/api/routes.js';
import { registerManagementRoutes } from './src/api/management.js';
import { registerTestingRoutes } from './src/api/testing.js';
import { validateSupabaseAuth } from './src/middleware/auth-simple.js';

// Get directory paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

// Configuration
const {
    GEMINI_API_KEY,
    PORT = 3000,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    PUBLIC_URL,
    SUMMARY_GENERATION_PROMPT = 'Report campaign related result and important context or follow up.',
    AI_PREPARE_MESSAGE = '' // Campaign script should provide all instructions
} = process.env;

// Get default voice and model from managers
const GEMINI_DEFAULT_VOICE = voiceManager.getDefaultVoice();
const GEMINI_DEFAULT_MODEL = modelManager.getDefaultModel();

console.log('🚀 Twilio Gemini Live API Server starting...');
console.log('📝 Using Gemini API Key:', GEMINI_API_KEY ? 'SET ✅' : 'NOT SET ❌');
console.log('📞 Twilio Config:', TWILIO_ACCOUNT_SID ? 'SET ✅' : 'NOT SET ❌');
console.log('🎤 Deepgram API Key:', process.env.DEEPGRAM_API_KEY ? 'SET ✅' : 'NOT SET ❌');
console.log('🔗 Public URL:', PUBLIC_URL || 'NOT SET');
console.log('🤖 Default Model:', GEMINI_DEFAULT_MODEL);
console.log('🎵 Default Voice:', GEMINI_DEFAULT_VOICE);

// Initialize core components
const geminiClient = initializeGeminiClient(GEMINI_API_KEY);
const contextManager = new ContextManager();
const sessionManager = new SessionManager(contextManager, geminiClient);
const healthMonitor = new ConnectionHealthMonitor();
const summaryManager = new SessionSummaryManager();
const lifecycleManager = new SessionLifecycleManager(contextManager, summaryManager, healthMonitor);
const recoveryManager = new SessionRecoveryManager(contextManager, healthMonitor, geminiClient);
const scriptManager = new ScriptManager();
const audioProcessor = new AudioProcessor();
const transcriptionManager = new TranscriptionManager();
const activeConnections = new Map();

// Initialize Fastify server
const fastify = Fastify({ logger: false });

// Register plugins
await fastify.register(fastifyWs);
await fastify.register(fastifyCors, {
    origin: true,
    credentials: true
});
await fastify.register(fastifyFormBody);

// Register authentication middleware
fastify.addHook('preHandler', validateSupabaseAuth);

// Register compression for better performance
await fastify.register(fastifyCompress, {
    global: true,
    threshold: 1024 // Only compress responses larger than 1KB
});

// Register rate limiting for security
await fastify.register(fastifyRateLimit, {
    max: 100, // Maximum 100 requests
    timeWindow: '1 minute', // Per minute
    skipOnError: true // Don't count failed requests
});

// Register static file serving for management interfaces
await fastify.register(fastifyStatic, {
    root: path.join(__dirname, 'static'),
    prefix: '/static/'
});

// Dependencies object for modules
const dependencies = {
    geminiClient,
    contextManager,
    sessionManager,
    healthMonitor,
    summaryManager,
    lifecycleManager,
    recoveryManager,
    scriptManager,
    audioProcessor,
    transcriptionManager,
    activeConnections,
    voiceManager,
    modelManager,
    GEMINI_DEFAULT_VOICE,
    GEMINI_DEFAULT_MODEL,
    SUMMARY_GENERATION_PROMPT,
    AI_PREPARE_MESSAGE,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    PUBLIC_URL
};

// Register WebSocket handlers
console.log('🔧 Registering WebSocket handlers...');
registerWebSocketHandlers(fastify, dependencies);
console.log('✅ WebSocket handlers registered');

// Register API routes
console.log('🔧 Calling registerApiRoutes...');
try {
    registerApiRoutes(fastify, dependencies);
    console.log('✅ API routes registration completed');
} catch (error) {
    console.error('❌ Error in registerApiRoutes:', error);
    throw error;
}

// Register management routes
console.log('🔧 Registering management routes...');
registerManagementRoutes(fastify, dependencies);
console.log('✅ Management routes registered');

// Register testing routes
console.log('🔧 Registering testing routes...');
registerTestingRoutes(fastify, dependencies);
console.log('✅ Testing routes registered');

// === MANAGEMENT INTERFACE ROUTES ===

// Incoming call management interface
fastify.get('/incoming', async (_request, reply) => {
    try {
        const html = await import('fs/promises').then(fs =>
            fs.readFile(path.join(__dirname, 'incoming-manager.html'), 'utf8')
        );
        reply.header('Content-Type', 'text/html');
        return html;
    } catch (error) {
        console.error('❌ Error loading incoming call management interface:', error);
        reply.code(500).send({ error: 'Failed to load incoming call management interface' });
    }
});

// Outbound scripts management interface
fastify.get('/outbound-scripts', async (_request, reply) => {
    try {
        const html = await import('fs/promises').then(fs =>
            fs.readFile(path.join(__dirname, 'outbound-scripts-manager.html'), 'utf8')
        );
        reply.header('Content-Type', 'text/html');
        return html;
    } catch (error) {
        console.error('❌ Error loading outbound scripts management interface:', error);
        reply.code(500).send({ error: 'Failed to load outbound scripts management interface' });
    }
});

// Legacy route for backward compatibility
fastify.get('/scripts', async (_request, reply) => {
    return reply.redirect('/outbound-scripts');
});

// Cleanup old contexts and sessions periodically
setInterval(() => {
    contextManager.cleanupOldContexts();
    lifecycleManager.cleanupInactiveSessions();
}, 300000); // Every 5 minutes

// Start the server
const start = async () => {
    try {
        await fastify.listen({
            port: PORT,
            host: '0.0.0.0'
        });
        console.log(`🚀 Server listening on port ${PORT}`);
        console.log(`🔗 WebSocket endpoint: ws://localhost:${PORT}/media-stream`);
        console.log(`🧪 Local audio testing: ws://localhost:${PORT}/local-audio-session`);
        console.log(`🏥 Health check: http://localhost:${PORT}/health`);
        console.log(`📊 API documentation: http://localhost:${PORT}/`);

        // Log voice and model configuration
        modelManager.logConfiguration();
        console.log(`🎤 Default Voice: ${GEMINI_DEFAULT_VOICE} (${voiceManager.getVoiceInfo(GEMINI_DEFAULT_VOICE)?.characteristics || 'unknown'})`);
        console.log(`🎵 Available Voices: ${Object.keys(voiceManager.getAvailableVoices()).join(', ')}`);
        console.log(`🔧 Voice Selection: ${voiceManager.isVoiceSelectionEnabled() ? 'Enabled' : 'Disabled'}`);

        console.log('✅ Server is ready to handle calls!');
    } catch (err) {
        console.error('❌ Error starting server:', err);
        process.exit(1);
    }
};

start();