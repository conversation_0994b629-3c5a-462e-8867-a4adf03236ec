import Fastify from 'fastify';
import WebSocket from 'ws';
import dotenv from 'dotenv';
import fastifyFormBody from '@fastify/formbody';
import fastifyWs from '@fastify/websocket';
import fastifyCors from '@fastify/cors';
import fastifyStatic from '@fastify/static';
import twilio from 'twilio';
import fs from 'fs';
import { readFile, writeFile, access } from 'fs/promises';
import path from 'path';
import { mkdir } from 'fs/promises';
import { dirname } from 'path';
import { fileURLToPath } from 'url';
import fetch from 'node-fetch';
import { GoogleGenAI, LiveServerMessage, Modality } from '@google/genai';
import { parsePhoneNumber } from 'libphonenumber-js';
import { getTimezone } from 'countries-and-timezones';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

// Configuration
const {
    GEMINI_API_KEY,
    PORT,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    PUBLIC_URL,
    DEFAULT_SYSTEM_PROMPT,
    SUMMARY_GENERATION_PROMPT,
    AI_PREPARE_MESSAGE,
    GREETING_AUDIO_URL
} = process.env;

// According to CAMPAIGN_SCRIPT_POLICY.md: NO SYSTEM MESSAGES
// This file should be updated to use campaign scripts instead of system messages
// For now, marking as deprecated
console.warn('⚠️ index.gemini.js uses deprecated system message approach - should use campaign scripts');

// Use the summary prompt from .env only - no hardcoded fallback
const ACTUAL_SUMMARY_PROMPT = SUMMARY_GENERATION_PROMPT || "";
const SUMMARY_TIMEOUT_MS = 60000;

const DEFAULT_VOICE = 'Orus';
const GEMINI_MODEL = 'gemini-2.5-flash-preview-native-audio-dialog';

// Initialize Gemini client
const geminiClient = new GoogleGenAI({
    apiKey: GEMINI_API_KEY
});

// Map to store active WebSocket connections keyed by callSid
const activeConnections = new Map();

// Store the configuration for the next call
// DEPRECATED: This file should use campaign scripts instead of system messages
let nextCallConfig = {
    systemMessage: "DEPRECATED - USE CAMPAIGN SCRIPTS",
    voice: DEFAULT_VOICE,
    targetName: null,
    targetPhoneNumber: null
};

// Initialize Fastify
const fastify = Fastify({ logger: true });

// Register CORS plugin - Allow frontend origin
fastify.register(fastifyCors, {
  origin: (origin, cb) => {
    const allowedOrigins = [
        PUBLIC_URL,
        process.env.FRONTEND_URL || 'http://localhost:3011'
    ].filter(Boolean).map(url => new URL(url).origin);

    if (!origin || allowedOrigins.includes(new URL(origin).origin)) {
      cb(null, true); return;
    }
    if (process.env.NODE_ENV === 'development' && origin && new URL(origin).hostname === 'localhost' && new URL(origin).port === '3000') {
        cb(null, true); return;
    }
    cb(new Error("Not allowed by CORS"), false);
  }
});

fastify.register(fastifyFormBody);
fastify.register(fastifyWs);

// Static file serving registration
fastify.register(fastifyStatic, {
  root: __dirname,
  prefix: '/static/',
});
console.log(`Serving static files from ${__dirname} at /static/`);

// Root route
fastify.get('/', async (request, reply) => {
    return { message: 'Twilio Gemini Live API Server is running!' };
});

// Incoming call handler (Twilio Webhook)
fastify.all('/incoming-call', async (request, reply) => {
    const twiml = new twilio.twiml.VoiceResponse();

    // Play audio file if URL is provided in environment variables
    if (GREETING_AUDIO_URL && GREETING_AUDIO_URL !== 'https://your-domain.com/greeting.mp3') {
        console.log(`Playing greeting audio from: ${GREETING_AUDIO_URL}`);
        twiml.play({}, GREETING_AUDIO_URL);
    } else {
        console.log("GREETING_AUDIO_URL not set or is placeholder, skipping initial Play.");
    }

    // Connect the media stream
    const connect = twiml.connect();
    const publicUrl = new URL(PUBLIC_URL);
    const wsProtocol = publicUrl.protocol === 'https:' ? 'wss' : 'ws';
    const wsUrl = `${wsProtocol}://${publicUrl.host}/media-stream`;
    connect.stream({ url: wsUrl });
    console.log(`Generated TwiML with Stream URL: ${wsUrl}`);

    reply.header('Content-Type', 'application/xml');
    return twiml.toString();
});

// Call Status endpoint (Twilio Webhook)
fastify.post('/call-status', async (request, reply) => {
    const { CallSid: callSid, CallStatus: callStatus } = request.body;
    console.log(`[${callSid}] Call status update received: ${callStatus}`);
    const connectionData = activeConnections.get(callSid);

    if (callStatus === 'completed') {
        console.log(`[${callSid}] Call completed via status callback.`);
        if (connectionData && !connectionData.summaryRequested && !connectionData.summaryReceived) {
             console.log(`[${callSid}] Connection found, requesting summary via status callback.`);
             requestSummary(callSid, connectionData);
        } else if (!connectionData) {
            console.warn(`[${callSid}] Received 'completed' status, but no active connection found.`);
            const infoFilePath = path.join(__dirname, 'data', `${callSid}_info.json`);
            try { await access(infoFilePath); } catch {
                await saveSummaryInfo(callSid, "Call completed, but connection was already closed.", "neutral", callStatus, null, null);
            }
        } else {
             console.log(`[${callSid}] Received 'completed' status, but summary already requested or received.`);
        }
    } else if (['failed', 'canceled', 'no-answer', 'busy'].includes(callStatus)) {
         console.log(`[${callSid}] Call ended with non-completed status: ${callStatus}. Saving placeholder info.`);
         if (connectionData) {
             console.log(`[${callSid}] Cleaning up connection due to status: ${callStatus}.`);
             if (connectionData.summaryTimeoutId) {
                 console.log(`[${callSid}] Clearing summary timeout due to status: ${callStatus}.`);
                 clearTimeout(connectionData.summaryTimeoutId);
             }
             // Close Gemini session if open
             if (connectionData.geminiSession) {
                 console.log(`[${callSid}] Closing Gemini session due to status: ${callStatus}.`);
                 connectionData.geminiSession.close();
             }
             // Close Twilio WS if open
             if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
                 console.log(`[${callSid}] Closing Twilio WS due to status: ${callStatus}.`);
                 connectionData.twilioWs.close();
             }
             // Save placeholder info
             await saveSummaryInfo(callSid, null, "neutral", callStatus, connectionData.targetName, connectionData.targetPhoneNumber);
             activeConnections.delete(callSid);
             console.log(`[${callSid}] Connection data deleted from map.`);
         } else {
              console.warn(`[${callSid}] Received terminal status ${callStatus}, but no active connection found.`);
              const infoFilePath = path.join(__dirname, 'data', `${callSid}_info.json`);
              try { await access(infoFilePath); } catch {
                  await saveSummaryInfo(callSid, `Call ended with status: ${callStatus}. Connection already closed.`, "neutral", callStatus, null, null);
              }
         }
    }

    reply.send('OK');
});

// Update call configuration endpoint
fastify.post('/update-config', async (request, reply) => {
    try {
        // DEPRECATED: This endpoint should be updated to use campaign scripts
        console.warn('⚠️ /update-config endpoint is deprecated - use campaign scripts instead');

        const { systemMessage, voice, targetName, targetPhoneNumber } = request.body;

        nextCallConfig = {
            systemMessage: "DEPRECATED - USE CAMPAIGN SCRIPTS",
            voice: voice || DEFAULT_VOICE,
            targetName: targetName || null,
            targetPhoneNumber: targetPhoneNumber || null
        };
        
        console.log('Updated call configuration:', nextCallConfig);
        reply.send({ success: true, config: nextCallConfig });
    } catch (error) {
        console.error('Error updating config:', error);
        reply.code(500).send({ error: error.message });
    }
});

// Get configuration endpoint
fastify.get('/config', async (request, reply) => {
    reply.send(nextCallConfig);
});

// Call results endpoint
fastify.get('/call-results/:callSid', async (request, reply) => {
    const callSid = request.params.callSid;
    const infoFilePath = path.join(__dirname, 'data', `${callSid}_info.json`);
    
    try {
        const infoData = await readFile(infoFilePath, 'utf8');
        const info = JSON.parse(infoData);
        reply.send(info);
    } catch (error) {
        console.error(`Error reading call info for ${callSid}:`, error);
        reply.code(404).send({ error: 'Call information not found' });
    }
});

// WebSocket handler for media streams
fastify.register(async function (fastify) {
    fastify.get('/media-stream', { websocket: true }, (connection, req) => {
        console.log('New WebSocket connection');
        
        let callSid = null;
        let geminiSession = null;
        let isSessionActive = false;
        let streamSid = null;
        
        const connectionData = {
            twilioWs: connection.socket,
            geminiSession: null,
            targetName: nextCallConfig.targetName,
            targetPhoneNumber: nextCallConfig.targetPhoneNumber,
            summaryRequested: false,
            summaryReceived: false,
            summaryTimeoutId: null,
            conversationLog: []
        };

        connection.socket.on('message', async (message) => {
            try {
                const data = JSON.parse(message);
                
                switch (data.event) {
                    case 'connected':
                        console.log('Twilio connected');
                        break;
                        
                    case 'start':
                        callSid = data.start.callSid;
                        streamSid = data.start.streamSid;
                        console.log(`[${callSid}] Stream started`);
                        
                        // Store connection
                        activeConnections.set(callSid, connectionData);
                        
                        // Initialize Gemini Live session
                        await initializeGeminiSession(callSid, connectionData, streamSid);
                        break;
                        
                    case 'media':
                        if (geminiSession && isSessionActive) {
                            // Convert Twilio audio (base64 mulaw) to format for Gemini
                            const audioData = data.media.payload;
                            
                            // Send audio to Gemini (simplified - needs proper audio conversion)
                            geminiSession.send({
                                clientContent: {
                                    turns: [{
                                        role: 'user',
                                        parts: [{
                                            inlineData: {
                                                mimeType: 'audio/pcm',
                                                data: audioData
                                            }
                                        }]
                                    }]
                                }
                            });
                        }
                        break;
                        
                    case 'stop':
                        console.log(`[${callSid}] Stream stopped`);
                        if (geminiSession) {
                            geminiSession.close();
                        }
                        
                        // Request summary
                        if (connectionData && !connectionData.summaryRequested) {
                            requestSummary(callSid, connectionData);
                        }
                        break;
                }
            } catch (error) {
                console.error('Error processing message:', error);
            }
        });

        connection.socket.on('close', () => {
            console.log(`[${callSid}] WebSocket connection closed`);
            if (geminiSession) {
                geminiSession.close();
            }
            if (callSid) {
                activeConnections.delete(callSid);
            }
        });

        async function initializeGeminiSession(callSid, connectionData, streamSid) {
            try {
                console.log(`[${callSid}] Initializing Gemini Live session`);
                
                geminiSession = await geminiClient.live.connect({
                    model: GEMINI_MODEL,
                    callbacks: {
                        onopen: () => {
                            console.log(`[${callSid}] Gemini session opened`);
                            isSessionActive = true;
                            
                            // Send system message
                            geminiSession.send({
                                clientContent: {
                                    turns: [{
                                        role: 'user',
                                        parts: [{
                                            text: "DEPRECATED FILE - USE MAIN INDEX.JS WITH CAMPAIGN SCRIPTS"
                                        }]
                                    }]
                                }
                            });
                        },
                        
                        onmessage: async (message) => {
                            try {
                                // Handle audio response from Gemini
                                const audio = message.serverContent?.modelTurn?.parts?.[0]?.inlineData;
                                
                                if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                    // Send audio back to Twilio (simplified conversion)
                                    connection.socket.send(JSON.stringify({
                                        event: 'media',
                                        streamSid: streamSid,
                                        media: {
                                            payload: audio.data
                                        }
                                    }));
                                }
                                
                                // Handle text response
                                const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                                if (text) {
                                    console.log(`[${callSid}] Gemini response: ${text}`);
                                    connectionData.conversationLog.push({
                                        role: 'assistant',
                                        content: text,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            } catch (error) {
                                console.error(`[${callSid}] Error processing Gemini message:`, error);
                            }
                        },
                        
                        onerror: (error) => {
                            console.error(`[${callSid}] Gemini session error:`, error);
                            isSessionActive = false;
                        },
                        
                        onclose: () => {
                            console.log(`[${callSid}] Gemini session closed`);
                            isSessionActive = false;
                        }
                    },
                    config: {
                        responseModalities: [Modality.AUDIO, Modality.TEXT],
                        speechConfig: {
                            voiceConfig: { 
                                prebuiltVoiceConfig: { 
                                    voiceName: nextCallConfig.voice 
                                } 
                            }
                        }
                    }
                });
                
                connectionData.geminiSession = geminiSession;
                
            } catch (error) {
                console.error(`[${callSid}] Error initializing Gemini session:`, error);
            }
        }
    });
});

// Summary and data saving functions
async function saveSummaryInfo(callSid, rawSummaryText, defaultSentiment = "neutral", status = "completed", targetName = null, targetPhoneNumber = null) {
    try {
        const dataDir = path.join(__dirname, 'data');
        await mkdir(dataDir, { recursive: true });

        const summaryData = {
            callSid,
            summary: rawSummaryText || "No summary available",
            sentiment: defaultSentiment,
            status,
            targetName,
            targetPhoneNumber,
            timestamp: new Date().toISOString(),
            processedAt: new Date().toISOString()
        };

        const filePath = path.join(dataDir, `${callSid}_info.json`);
        await writeFile(filePath, JSON.stringify(summaryData, null, 2));
        console.log(`[${callSid}] Summary saved to ${filePath}`);
        
        return summaryData;
    } catch (error) {
        console.error(`[${callSid}] Error saving summary:`, error);
        throw error;
    }
}

async function requestSummary(callSid, connectionData) {
    try {
        console.log(`[${callSid}] Requesting summary from conversation log`);
        connectionData.summaryRequested = true;

        // Generate summary from conversation log
        const conversationText = connectionData.conversationLog
            .map(entry => `${entry.role}: ${entry.content}`)
            .join('\n');

        let summary = "Call completed";
        if (conversationText) {
            summary = `Summary: ${conversationText.substring(0, 500)}${conversationText.length > 500 ? '...' : ''}`;
        }

        await saveSummaryInfo(
            callSid,
            summary,
            "neutral",
            "completed",
            connectionData.targetName,
            connectionData.targetPhoneNumber
        );

        connectionData.summaryReceived = true;
        console.log(`[${callSid}] Summary processing completed`);

    } catch (error) {
        console.error(`[${callSid}] Error requesting summary:`, error);
        await saveSummaryInfo(
            callSid,
            "Error generating summary",
            "neutral",
            "error",
            connectionData.targetName,
            connectionData.targetPhoneNumber
        );
    }
}

// Health check endpoint
fastify.get('/health', async (request, reply) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Twilio Gemini Live API',
        model: GEMINI_MODEL,
        voice: DEFAULT_VOICE
    };
    reply.send(health);
});

// Start the server
const start = async () => {
    try {
        const port = PORT || 3101;
        await fastify.listen({ port, host: '0.0.0.0' });
        console.log(`Twilio Gemini Live API Server listening on port ${port}`);
        console.log(`Model: ${GEMINI_MODEL}`);
        console.log(`Default Voice: ${DEFAULT_VOICE}`);
    } catch (err) {
        fastify.log.error(err);
        process.exit(1);
    }
};

start(); 