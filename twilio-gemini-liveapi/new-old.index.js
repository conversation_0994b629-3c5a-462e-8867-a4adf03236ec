import Fastify from 'fastify';
import dotenv from 'dotenv';
import fastifyFormBody from '@fastify/formbody';
import fastifyWs from '@fastify/websocket';
import fastifyCors from '@fastify/cors';
import fastifyStatic from '@fastify/static';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import path from 'path';

// Import modular components
import { initializeGeminiClient } from './src/gemini/client.js';
import { ContextManager } from './src/session/context-manager.js';
import { SessionManager } from './src/session/session-manager.js';
import { AudioProcessor } from './src/audio/audio-processor.js';
import { registerWebSocketHandlers } from './src/websocket/handlers.js';
import { registerApiRoutes } from './src/api/routes.js';
import { registerManagementRoutes } from './src/api/management.js';
import { registerTestingRoutes } from './src/api/testing.js';
import { validateSupabaseAuth } from './src/middleware/auth-simple.js';

// Get directory paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

// Configuration
const {
    GEMINI_API_KEY,
    PORT = 3000,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    PUBLIC_URL,
    SUMMARY_GENERATION_PROMPT = 'Report campaign related result and important context or follow up.',
    AI_PREPARE_MESSAGE = '', // Campaign script should provide all instructions
    GEMINI_DEFAULT_VOICE = 'Kore',
    GEMINI_DEFAULT_MODEL = 'gemini-2.5-flash-preview-native-audio-dialog'
} = process.env;

console.log('🚀 Twilio Gemini Live API Server starting...');
console.log('📝 Using Gemini API Key:', GEMINI_API_KEY ? 'SET ✅' : 'NOT SET ❌');
console.log('📞 Twilio Config:', TWILIO_ACCOUNT_SID ? 'SET ✅' : 'NOT SET ❌');

// Initialize core components
const geminiClient = initializeGeminiClient(GEMINI_API_KEY);
const contextManager = new ContextManager();
const sessionManager = new SessionManager(contextManager, geminiClient);
const activeConnections = new Map();

// Export for use in other modules
export {
    geminiClient,
    contextManager,
    sessionManager,
    activeConnections,
    GEMINI_DEFAULT_VOICE,
    GEMINI_DEFAULT_MODEL,
    SUMMARY_GENERATION_PROMPT,
    AI_PREPARE_MESSAGE
};

// Context Manager for session recovery
class ContextManager {
    constructor() {
        this.contextStore = new Map();
        this.recoveryAttempts = new Map();
        this.maxRecoveryAttempts = 3;
    }

    // Save session context for recovery
    saveSessionContext(callSid, context) {
        const timestamp = Date.now();
        const sessionContext = {
            callSid,
            timestamp,
            sessionConfig: {
                aiInstructions: context.aiInstructions,
                voice: context.voice,
                model: context.model,
                isIncomingCall: context.isIncomingCall
            },
            conversationState: {
                isSessionActive: context.isSessionActive,
                summaryRequested: context.summaryRequested,
                summaryText: context.summaryText || '',
                conversationLog: context.conversationLog || []
            },
            connectionState: {
                streamSid: context.streamSid,
                lastActivity: timestamp
            },
            recoveryInfo: {
                lastRecoveryTime: null,
                recoveryCount: this.recoveryAttempts.get(callSid) || 0,
                wasInterrupted: false
            }
        };

        this.contextStore.set(callSid, sessionContext);
        console.log(`💾 [${callSid}] Session context saved`);
        return sessionContext;
    }

    // Get session context for recovery
    getSessionContext(callSid) {
        return this.contextStore.get(callSid);
    }

    // Mark session as interrupted
    markSessionInterrupted(callSid, reason = 'unknown') {
        const context = this.contextStore.get(callSid);
        if (context) {
            context.recoveryInfo.wasInterrupted = true;
            context.recoveryInfo.interruptionReason = reason;
            context.recoveryInfo.interruptionTime = Date.now();
            this.contextStore.set(callSid, context);
            console.log(`⚠️ [${callSid}] Session marked as interrupted: ${reason}`);
        }
    }

    // Check if session can be recovered
    canRecover(callSid) {
        const context = this.contextStore.get(callSid);
        if (!context) {return false;}

        const recoveryCount = this.recoveryAttempts.get(callSid) || 0;
        const timeSinceLastActivity = Date.now() - context.connectionState.lastActivity;
        const maxRecoveryTime = 300000; // 5 minutes

        return recoveryCount < this.maxRecoveryAttempts && timeSinceLastActivity < maxRecoveryTime;
    }

    // Increment recovery attempt counter
    incrementRecoveryAttempt(callSid) {
        const current = this.recoveryAttempts.get(callSid) || 0;
        this.recoveryAttempts.set(callSid, current + 1);
        return current + 1;
    }

    // Get recovery message with conversation context
    getRecoveryMessage(callSid) {
        const context = this.contextStore.get(callSid);
        if (!context || !context.recoveryInfo.wasInterrupted) {return null;}

        const recoveryCount = context.recoveryInfo.recoveryCount;
        let fullConversationHistory = '';

        if (context.conversationState?.conversationLog && context.conversationState.conversationLog.length > 0) {
            const conversationLog = context.conversationState.conversationLog.map(msg => {
                const speaker = msg.role === 'assistant' ? 'AI Assistant' : 'Customer';
                const timestamp = new Date(msg.timestamp).toLocaleTimeString();
                return `[${timestamp}] ${speaker}: ${msg.content}`;
            }).join('\n');

            fullConversationHistory = `\n\nCOMPLETE CONVERSATION HISTORY:\n${conversationLog}\n`;
        }

        const originalInstructions = context.sessionConfig?.aiInstructions || '';

        return `${originalInstructions}

[RECOVERY NOTICE]
You were briefly disconnected during the conversation. This is recovery attempt ${recoveryCount}.
You must continue the conversation seamlessly from exactly where it was interrupted.
${fullConversationHistory}
CRITICAL INSTRUCTIONS:
1. Resume the conversation naturally without mentioning any technical issues
2. Continue from the last point in the conversation above
3. Maintain the same tone and context as before the interruption
4. The customer should not notice any disruption in service
5. Do NOT acknowledge this recovery message to the customer

Continue the conversation now:`;
    }

    // Clear session context
    clearSessionContext(callSid) {
        this.contextStore.delete(callSid);
        this.recoveryAttempts.delete(callSid);
        console.log(`🗑️ [${callSid}] Session context cleared`);
    }
}

// Initialize context manager
const contextManager = new ContextManager();

// Basic Audio Processing utilities
class AudioProcessor {
    // Convert Twilio μ-law to PCM for Gemini
    static convertUlawToPCM(audioBuffer) {
        try {
            const ULAW_TO_LINEAR = new Int16Array(256);
            for (let i = 0; i < 256; i++) {
                const ulaw = ~i;
                let t = ((ulaw & 0x0F) << 3) + 0x84;
                t <<= ((ulaw & 0x70) >> 4);
                ULAW_TO_LINEAR[i] = (ulaw & 0x80) ? (0x84 - t) : (t - 0x84);
            }

            const int16Buffer = Buffer.alloc(audioBuffer.length * 2);
            for (let i = 0; i < audioBuffer.length; i++) {
                const ulawSample = audioBuffer[i];
                const pcmSample = ULAW_TO_LINEAR[ulawSample];
                int16Buffer.writeInt16LE(pcmSample, i * 2);
            }
            return int16Buffer;
        } catch (error) {
            console.error('❌ Error in μ-law to PCM conversion:', error);
            return audioBuffer;
        }
    }

    // Convert PCM to Float32Array for Gemini
    static pcmToFloat32Array(pcmBuffer) {
        try {
            const float32Array = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }
            return float32Array;
        } catch (error) {
            console.error('❌ Error in PCM to Float32 conversion:', error);
            return new Float32Array(0);
        }
    }

    // Upsample from 8kHz to 16kHz for Gemini
    static upsample8kTo16k(data) {
        const outputLength = data.length * 2;
        const output = new Float32Array(outputLength);

        for (let i = 0; i < data.length - 1; i++) {
            const current = data[i];
            const next = data[i + 1];
            output[i * 2] = current;
            output[i * 2 + 1] = (current + next) / 2;
        }

        if (data.length > 0) {
            output[outputLength - 2] = data[data.length - 1];
            output[outputLength - 1] = data[data.length - 1];
        }

        return output;
    }

    // Create audio blob for Gemini API
    static createGeminiAudioBlob(data) {
        const upsampled = AudioProcessor.upsample8kTo16k(data);
        const int16 = new Int16Array(upsampled.length);

        for (let i = 0; i < upsampled.length; i++) {
            int16[i] = upsampled[i] * 32768;
        }

        return {
            data: Buffer.from(int16.buffer).toString('base64'),
            mimeType: 'audio/pcm;rate=16000'
        };
    }

    // Convert Gemini PCM back to μ-law for Twilio
    static convertPCMToUlaw(base64Audio) {
        try {
            const pcmBuffer = Buffer.from(base64Audio, 'base64');
            const float32Array = new Float32Array(pcmBuffer.length / 2);

            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            // Downsample from 24kHz to 8kHz (simple decimation)
            const downsampled = new Float32Array(Math.floor(float32Array.length / 3));
            for (let i = 0; i < downsampled.length; i++) {
                downsampled[i] = float32Array[i * 3];
            }

            const ulawBuffer = Buffer.alloc(downsampled.length);
            for (let i = 0; i < downsampled.length; i++) {
                const pcmSample = Math.max(-32767, Math.min(32767, Math.round(downsampled[i] * 32767)));
                ulawBuffer[i] = AudioProcessor.linearToUlaw(pcmSample);
            }

            return ulawBuffer.toString('base64');
        } catch (error) {
            console.error('❌ Error converting PCM to μ-law:', error);
            return base64Audio;
        }
    }

    // Convert linear PCM sample to μ-law
    static linearToUlaw(pcm) {
        const BIAS = 0x84;
        const CLIP = 32635;

        const sign = (pcm >> 8) & 0x80;
        if (sign !== 0) {pcm = -pcm;}
        if (pcm > CLIP) {pcm = CLIP;}

        pcm += BIAS;
        let exponent = 7;
        let expMask = 0x4000;

        for (let i = 0; i < 8; i++) {
            if ((pcm & expMask) !== 0) {break;}
            exponent--;
            expMask >>= 1;
        }

        const mantissa = (pcm >> (exponent + 3)) & 0x0F;
        const ulaw = ~(sign | (exponent << 4) | mantissa);

        return ulaw & 0xFF;
    }
}

// Session Manager for Gemini connections with recovery
class SessionManager {
    constructor(contextManager) {
        this.contextManager = contextManager;
        this.recoveryInProgress = new Set();
    }

    // Create new Gemini session
    async createGeminiSession(callSid, config, connectionData) {
        try {
            console.log(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);

            const geminiSession = await geminiClient.live.connect({
                model: config.model,
                callbacks: {
                    onopen: () => {
                        console.log(`✅ [${callSid}] Gemini session opened`);
                        connectionData.isSessionActive = true;

                        // Save initial context
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...connectionData,
                            conversationLog: []
                        });

                        // Send initial AI prepare message
                        this.sendInitialMessage(geminiSession, config.aiInstructions);
                    },

                    onerror: (error) => {
                        console.error(`❌ [${callSid}] Gemini session error:`, error);
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');

                        // Attempt recovery if possible
                        if (this.contextManager.canRecover(callSid)) {
                            setTimeout(() => {
                                this.recoverSession(callSid, 'session_error');
                            }, 2000);
                        }
                    },

                    onclose: () => {
                        console.log(`🔌 [${callSid}] Gemini session closed`);
                        connectionData.isSessionActive = false;

                        // Only mark as interrupted if this wasn't a manual close
                        if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed');

                            // Attempt recovery
                            if (this.contextManager.canRecover(callSid)) {
                                setTimeout(() => {
                                    this.recoverSession(callSid, 'session_closed');
                                }, 1000);
                            }
                        }
                    },

                    onmessage: async (message) => {
                        await this.handleGeminiMessage(callSid, message, connectionData);
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192,
                responseMimeType: 'text/plain'
            });

            return geminiSession;

        } catch (error) {
            console.error(`❌ [${callSid}] Error creating Gemini session:`, error);
            return null;
        }
    }

    // Handle messages from Gemini
    async handleGeminiMessage(callSid, message, connectionData) {
        try {
            // Handle audio response
            if (message?.serverContent?.modelTurn?.parts?.[0]?.inlineData) {
                const audio = message.serverContent.modelTurn.parts[0].inlineData;

                if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                    if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
                        const convertedAudio = AudioProcessor.convertPCMToUlaw(audio.data);
                        const audioDelta = {
                            event: 'media',
                            streamSid: connectionData.streamSid,
                            media: { payload: convertedAudio }
                        };
                        connectionData.twilioWs.send(JSON.stringify(audioDelta));
                        console.log(`🔊 [${callSid}] Sent audio to Twilio`);
                    }
                }
            }

            // Handle text responses
            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
            if (text) {
                console.log(`💬 [${callSid}] AI response: ${text.substring(0, 100)}...`);

                // Add to conversation log
                if (connectionData.conversationLog) {
                    connectionData.conversationLog.push({
                        role: 'assistant',
                        content: text,
                        timestamp: Date.now()
                    });
                }

                // Handle summary if requested
                if (connectionData.summaryRequested) {
                    connectionData.summaryText += text;
                }

                // Update context
                this.contextManager.saveSessionContext(callSid, connectionData);
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error handling Gemini message:`, error);
        }
    }

    // Send initial message to AI
    async sendInitialMessage(geminiSession, aiInstructions) {
        try {
            if (geminiSession && aiInstructions) {
                await geminiSession.sendClientContent({
                    turns: [{
                        role: 'user',
                        parts: [{
                            text: aiInstructions
                        }]
                    }],
                    turnComplete: true
                });
                console.log('📤 Initial AI instructions sent');
            }
        } catch (error) {
            console.error('❌ Error sending initial message:', error);
        }
    }

    // Recover session after interruption
    async recoverSession(callSid, reason) {
        if (this.recoveryInProgress.has(callSid)) {
            console.log(`⏳ [${callSid}] Recovery already in progress`);
            return;
        }

        this.recoveryInProgress.add(callSid);

        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                console.log(`❌ [${callSid}] Cannot recover session`);
                return;
            }

            const connectionData = activeConnections.get(callSid);
            if (!connectionData) {
                console.log(`❌ [${callSid}] No connection data for recovery`);
                return;
            }

            const recoveryCount = this.contextManager.incrementRecoveryAttempt(callSid);
            console.log(`🔄 [${callSid}] Attempting session recovery #${recoveryCount} (reason: ${reason})`);

            // Create new session
            const newGeminiSession = await this.createGeminiSession(callSid, context.sessionConfig, connectionData);

            if (newGeminiSession) {
                connectionData.geminiSession = newGeminiSession;

                // Send recovery message with conversation context
                const recoveryMessage = this.contextManager.getRecoveryMessage(callSid);
                if (recoveryMessage) {
                    await this.sendInitialMessage(newGeminiSession, recoveryMessage);
                }

                console.log(`✅ [${callSid}] Session recovered successfully`);
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error during session recovery:`, error);
        } finally {
            this.recoveryInProgress.delete(callSid);
        }
    }
}

// Initialize session manager
const sessionManager = new SessionManager(contextManager);

// Summary generation functionality
async function generateSummary(callSid, connectionData) {
    try {
        console.log(`📋 [${callSid}] Generating call summary`);

        if (!connectionData.geminiSession) {
            console.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
            return;
        }

        connectionData.summaryRequested = true;
        connectionData.summaryText = '';

        // Send summary request
        await connectionData.geminiSession.sendClientContent({
            turns: [{
                role: 'user',
                parts: [{
                    text: SUMMARY_GENERATION_PROMPT || '' // No hardcoded fallback
                }]
            }],
            turnComplete: true
        });

        // Wait for summary response (with timeout)
        setTimeout(() => {
            if (connectionData.summaryRequested) {
                console.log(`⏰ [${callSid}] Summary generation timeout`);
                connectionData.summaryRequested = false;
                endSession(callSid);
            }
        }, 30000); // 30 second timeout

        // The summary will be collected in handleGeminiMessage
        // and endSession will be called when complete

    } catch (error) {
        console.error(`❌ [${callSid}] Error generating summary:`, error);
        endSession(callSid);
    }
}

// End session and cleanup
function endSession(callSid) {
    try {
        console.log(`🔚 [${callSid}] Ending session`);

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // Close Gemini session
            if (connectionData.geminiSession) {
                try {
                    connectionData.geminiSession.close();
                } catch (error) {
                    console.warn(`⚠️ [${callSid}] Error closing Gemini session:`, error);
                }
            }

            // Close Twilio WebSocket
            if (connectionData.twilioWs && connectionData.twilioWs.readyState === WebSocket.OPEN) {
                try {
                    connectionData.twilioWs.close();
                } catch (error) {
                    console.warn(`⚠️ [${callSid}] Error closing Twilio WebSocket:`, error);
                }
            }

            // Log summary if available
            if (connectionData.summaryText) {
                console.log(`📋 [${callSid}] Call Summary: ${connectionData.summaryText}`);
            }

            // Remove from active connections
            activeConnections.delete(callSid);
        }

        // Clear context
        contextManager.clearSessionContext(callSid);

        console.log(`✅ [${callSid}] Session ended and cleaned up`);

    } catch (error) {
        console.error(`❌ [${callSid}] Error ending session:`, error);
    }
}

// Initialize Fastify server
const fastify = Fastify({ logger: false });

// Register plugins
await fastify.register(fastifyWs);
await fastify.register(fastifyCors, {
    origin: true,
    credentials: true
});
await fastify.register(fastifyFormBody);

// Default configuration for calls - no hardcoded instructions
let nextCallConfig = {
    aiInstructions: '', // Campaign script should provide all instructions
    voice: GEMINI_DEFAULT_VOICE,
    model: GEMINI_DEFAULT_MODEL,
    targetName: null,
    targetPhoneNumber: null
};

// WebSocket handler for Twilio media streams
fastify.register(async (fastify) => {
    fastify.get('/media-stream', { websocket: true }, (connection, _req) => {
        let callSid = 'pending';
        console.log(`🔌 [${callSid}] Client connected to /media-stream`);

        let streamSid = null;
        let geminiSession = null;
        let isSessionActive = false;

        // Determine if this is incoming or outbound call
        const isIncomingCall = !nextCallConfig.aiInstructions ||
            nextCallConfig.aiInstructions.includes('helpful AI assistant');

        // Use current config and reset for next call
        const sessionConfig = { ...nextCallConfig };
        nextCallConfig = {
            aiInstructions: '', // Campaign script should provide all instructions
            voice: GEMINI_DEFAULT_VOICE,
            model: GEMINI_DEFAULT_MODEL,
            targetName: null,
            targetPhoneNumber: null
        };

        console.log(`📞 [${callSid}] Call type: ${isIncomingCall ? 'INCOMING' : 'OUTBOUND'}`);
        console.log(`🤖 [${callSid}] Using model: ${sessionConfig.model}, voice: ${sessionConfig.voice}`);

        connection.socket.on('message', async (message) => {
            try {
                const data = JSON.parse(message);

                switch (data.event) {
                    case 'connected':
                        console.log(`📞 [${callSid}] Connected event received`);
                        break;

                    case 'start':
                        streamSid = data.start.streamSid;
                        callSid = data.start.callSid;
                        console.log(`🚀 [${callSid}] Stream started: ${streamSid}`);

                        // Store connection data
                        const connectionData = {
                            twilioWs: connection.socket,
                            streamSid,
                            callSid,
                            isSessionActive: false,
                            summaryRequested: false,
                            summaryText: '',
                            conversationLog: [],
                            isIncomingCall
                        };
                        activeConnections.set(callSid, connectionData);

                        // Create Gemini session
                        geminiSession = await sessionManager.createGeminiSession(callSid, sessionConfig, connectionData);
                        if (geminiSession) {
                            connectionData.geminiSession = geminiSession;
                            isSessionActive = true;
                        }
                        break;

                    case 'media':
                        if (geminiSession && isSessionActive && data.media.payload) {
                            try {
                                // Convert Twilio audio to Gemini format
                                const audioBuffer = Buffer.from(data.media.payload, 'base64');
                                const pcmBuffer = AudioProcessor.convertUlawToPCM(audioBuffer);
                                const float32Data = AudioProcessor.pcmToFloat32Array(pcmBuffer);
                                const audioBlob = AudioProcessor.createGeminiAudioBlob(float32Data);

                                // Send to Gemini
                                await geminiSession.sendClientContent({
                                    turns: [{
                                        role: 'user',
                                        parts: [{
                                            inlineData: audioBlob
                                        }]
                                    }],
                                    turnComplete: false
                                });

                            } catch (error) {
                                console.error(`❌ [${callSid}] Error processing audio:`, error);
                            }
                        }
                        break;

                    case 'stop':
                        console.log(`🛑 [${callSid}] Stream stopped`);

                        // Generate summary before ending
                        const stopConnectionData = activeConnections.get(callSid);
                        if (stopConnectionData && stopConnectionData.conversationLog.length > 0) {
                            await generateSummary(callSid, stopConnectionData);
                        } else {
                            endSession(callSid);
                        }
                        break;

                    default:
                        console.log(`❓ [${callSid}] Unknown event: ${data.event}`);
                }

            } catch (error) {
                console.error(`❌ [${callSid}] Error processing message:`, error);
            }
        });

        connection.socket.on('close', () => {
            console.log(`🔌 [${callSid}] WebSocket connection closed`);
            endSession(callSid);
        });

        connection.socket.on('error', (error) => {
            console.error(`❌ [${callSid}] WebSocket error:`, error);
            endSession(callSid);
        });
    });
});

// Root route
fastify.get('/', async (_request, _reply) => {
    return {
        service: 'Twilio Gemini Live API',
        status: 'running',
        version: '1.0.0',
        endpoints: {
            health: '/health',
            websocket: '/media-stream',
            configure: '/configure-call',
            endSession: '/end-session/:callSid'
        }
    };
});

// Basic health check route
fastify.get('/health', async (_request, _reply) => {
    return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        activeConnections: activeConnections.size
    };
});

// Route to configure next outbound call
fastify.post('/configure-call', async (request, _reply) => {
    try {
        const { aiInstructions, voice, model, targetName, targetPhoneNumber } = request.body;

        nextCallConfig = {
            aiInstructions: aiInstructions || nextCallConfig.aiInstructions,
            voice: voice || GEMINI_DEFAULT_VOICE,
            model: model || GEMINI_DEFAULT_MODEL,
            targetName: targetName || null,
            targetPhoneNumber: targetPhoneNumber || null
        };

        console.log(`⚙️ Call configured for: ${nextCallConfig.targetName}`);

        return {
            success: true,
            config: nextCallConfig
        };
    } catch (error) {
        console.error('❌ Error configuring call:', error);
        return {
            success: false,
            error: error.message
        };
    }
});

// Route to manually end a session
fastify.post('/end-session/:callSid', async (request, _reply) => {
    try {
        const { callSid } = request.params;

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // Generate summary if conversation exists
            if (connectionData.conversationLog.length > 0) {
                await generateSummary(callSid, connectionData);
            } else {
                endSession(callSid);
            }

            return { success: true, message: 'Session ending initiated' };
        } else {
            return { success: false, message: 'Session not found' };
        }
    } catch (error) {
        console.error('❌ Error ending session:', error);
        return { success: false, error: error.message };
    }
});

// Start the server
const start = async () => {
    try {
        await fastify.listen({
            port: PORT,
            host: '0.0.0.0'
        });
        console.log(`🚀 Server listening on port ${PORT}`);
        console.log(`🔗 WebSocket endpoint: ws://localhost:${PORT}/media-stream`);
        console.log(`🏥 Health check: http://localhost:${PORT}/health`);
    } catch (err) {
        console.error('❌ Error starting server:', err);
        process.exit(1);
    }
};

start();